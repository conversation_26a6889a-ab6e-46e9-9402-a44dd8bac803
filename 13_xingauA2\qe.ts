const { ccclass, property } = cc._decorator;

@ccclass
class QRCodeGenerator extends cc.Component {
    @property(cc.EditBox)
    private amountInput: cc.EditBox = null; // Ô nhập số tiền

    @property(cc.Button)
    private confirmButton: cc.Button = null; // Nút xác nhận

    @property(cc.Sprite)
    private qrSprite: cc.Sprite = null; // Sprite hiển thị QR code

    @property(cc.Label)
    private randomLabel: cc.Label = null; // Label hiển thị chuỗi ngẫu nhiên

    private qrCodeUrl: string = "https://img.vietqr.io/image/MB-005544220-compact.png";

    onLoad() {
        // Gắn sự kiện click cho nút xác nhận
        this.confirmButton.node.on('click', this.generateQRCode, this);
    }

    // Hàm tạo chuỗi ngẫu nhiên 10 ký tự (A-Z, 0-9)
    private generateRandomString(length: number = 10): string {
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            const randomIndex = Math.floor(Math.random() * characters.length);
            result += characters.charAt(randomIndex);
        }
        return result;
    }

    // Hàm tạo và hiển thị QR code
    private generateQRCode() {
        // Lấy số tiền từ EditBox
        const amount = this.amountInput.string.trim();
        if (!amount || isNaN(Number(amount)) || Number(amount) <= 0) {
            console.error("Vui lòng nhập số tiền hợp lệ!");
            return;
        }

        // Tạo chuỗi ngẫu nhiên cho addInfo
        const randomAddInfo = this.generateRandomString();
        this.randomLabel.string = randomAddInfo; // Hiển thị chuỗi ngẫu nhiên lên Label

        // Tạo URL cho VietQR API
        const url = `${this.qrCodeUrl}?amount=${encodeURIComponent(amount)}&addInfo=${encodeURIComponent(randomAddInfo)}`;

        // Tải và hiển thị QR code lên Sprite
        this.loadQRCode(url);
    }

    // Hàm tải hình ảnh QR code từ URL và hiển thị lên Sprite
    private loadQRCode(url: string) {
        cc.loader.load({ url: url, type: 'png' }, (err, texture) => {
            if (err) {
                console.error("Lỗi khi tải QR code từ URL:", err);
                return;
            }

            // Tạo SpriteFrame từ texture
            const spriteFrame = new cc.SpriteFrame();
            spriteFrame.setTexture(texture);

            // Gán SpriteFrame vào Sprite
            this.qrSprite.spriteFrame = spriteFrame;
        });
    }
}
{"skeleton": {"hash": "PhQQybpsWd1cUu92evrRjI6ijTc", "spine": "3.7.94", "width": 475.26, "height": 269.63, "images": "./images/", "audio": "F:/Project/Casino/Baccarat"}, "bones": [{"name": "root"}, {"name": "Dealer", "parent": "root"}, {"name": "Hip", "parent": "Dealer"}, {"name": "Body", "parent": "Hip", "length": 15, "rotation": 90, "y": 28.16}, {"name": "Chest", "parent": "Body", "x": 57.07, "color": "abe323ff"}, {"name": "<PERSON><PERSON><PERSON>", "parent": "Chest", "x": -28.23, "y": -0.18}, {"name": "Breast Left", "parent": "<PERSON><PERSON><PERSON>", "x": -0.23, "y": -29}, {"name": "Breast Right", "parent": "<PERSON><PERSON><PERSON>", "x": -0.23, "y": 28}, {"name": "Neck", "parent": "Body", "rotation": -0.36, "x": 82.82, "color": "abe323ff"}, {"name": "<PERSON><PERSON><PERSON>", "parent": "Body", "length": 25, "rotation": -65, "x": 77.93, "y": -13.31}, {"name": "Clavicle Right", "parent": "Body", "length": 25, "rotation": 65, "x": 78.12, "y": 13.31}, {"name": "Arm Left", "parent": "<PERSON><PERSON><PERSON>", "length": 75, "rotation": -90.36, "x": 23.99, "y": -5.52}, {"name": "Arm Right", "parent": "Clavicle Right", "length": 75, "rotation": 89.64, "x": 23.06, "y": 4.18}, {"name": "<PERSON>earm Left", "parent": "Arm Left", "length": 60, "rotation": -27, "x": 90.33, "y": -6.18, "scaleX": 0.85, "color": "abe323ff"}, {"name": "Forearm Right", "parent": "Arm Right", "length": 60, "rotation": 27, "x": 90.43, "y": 4.6, "scaleX": 0.85, "scaleY": 0.996, "color": "abe323ff"}, {"name": "Body 3D Horizontal", "parent": "Body", "x": -78.16, "color": "ff3200ff"}, {"name": "Body 3D Vertical", "parent": "Body 3D Horizontal", "rotation": -90, "color": "abe323ff"}, {"name": "Body Positive", "parent": "Body", "rotation": -90.36, "x": -128.16, "y": 0.82}, {"name": "Body Negative", "parent": "Body", "rotation": -90.36, "x": -178.16, "y": 1.13}, {"name": "Head", "parent": "Neck", "x": 14, "scaleX": 0.975, "scaleY": 0.972, "color": "abe323ff"}, {"name": "Head 3D Bend Over", "parent": "Head", "x": 124.56, "y": -0.23, "color": "abe323ff"}, {"name": "Head 3D Horizontal", "parent": "Head 3D Bend Over", "rotation": 0.36, "x": -1.41, "y": 2.3, "color": "ff3200ff"}, {"name": "Head 3D Vertical", "parent": "Head 3D Horizontal", "color": "abe323ff"}, {"name": "Head Positive", "parent": "Head", "x": 175}, {"name": "Head Negative", "parent": "Head", "x": 225}, {"name": "Body Bend Over", "parent": "Body", "rotation": -90.36, "x": 72.79, "y": 149.54, "color": "abe323ff"}, {"name": "Hip Bend Over", "parent": "Hip", "x": 150, "y": 100, "color": "abe323ff"}, {"name": "Hair Top", "parent": "Head", "x": 59.51, "y": -0.99}, {"name": "Hair Right", "parent": "Hair Top", "rotation": 159.21, "x": -21.78, "y": 22.39}, {"name": "Hair Right Head Rotation", "parent": "Hair Right"}, {"name": "Hair Right 1", "parent": "Hair Right Head Rotation", "length": 15.75}, {"name": "Hair Right 2", "parent": "Hair Right 1", "length": 16.28, "rotation": 5.02, "x": 15.81, "y": -0.14}, {"name": "Hair Right 3", "parent": "Hair Right 2", "length": 18.9, "rotation": 25.42, "x": 16.22, "y": -0.12}, {"name": "Hair Right 4", "parent": "Hair Right 3", "length": 22.26, "rotation": 14.16, "x": 18.9}, {"name": "Hair Left", "parent": "Hair Top", "rotation": 175.19, "x": -32.68, "y": -24.65}, {"name": "Hair Left Head Rotation", "parent": "Hair Left"}, {"name": "Hair Left 1", "parent": "Hair Left Head Rotation", "length": 11.19}, {"name": "Hair Left 2", "parent": "Hair Left 1", "length": 10.12, "rotation": -15.99, "x": 11.17, "y": -0.08}, {"name": "Hair Left 3", "parent": "Hair Left 2", "length": 9.21, "rotation": 19.85, "x": 10.12}, {"name": "Hair Left 4", "parent": "Hair Left 3", "length": 9.45, "rotation": 36.77, "x": 9.21}, {"name": "Hair Left 5", "parent": "Hair Left 4", "length": 11.86, "rotation": -41.59, "x": 9.45}, {"name": "Hand Left Scale", "parent": "<PERSON>earm Left", "x": 66.5, "y": -0.65}, {"name": "Hand Left", "parent": "Hand Left Scale", "length": 26.53, "scaleX": 1.15}, {"name": "Finger Left 1", "parent": "Hand Left", "length": 6.68, "x": 26.53}, {"name": "Finger Left 2", "parent": "Finger Left 1", "length": 6.8, "x": 6.68}, {"name": "Finger Left 3", "parent": "Finger Left 2", "length": 7.72, "x": 6.8}, {"name": "Hand Right Scale", "parent": "Forearm Right", "x": 66.5, "y": 0.65}, {"name": "Hand Right", "parent": "Hand Right Scale", "length": 26.53, "scaleX": 1.15}, {"name": "Finger Right 1", "parent": "Hand Right", "length": 6.68, "x": 26.53}, {"name": "Finger Right 2", "parent": "Finger Right 1", "length": 6.8, "x": 6.68}, {"name": "Finger Right 3", "parent": "Finger Right 2", "length": 7.72, "x": 6.8}, {"name": "<PERSON><PERSON><PERSON> Left IK", "parent": "Dealer", "x": 55, "y": -18, "color": "ff3f00ff"}, {"name": "Forearm Right IK", "parent": "Dealer", "x": -55, "y": -18, "color": "ff3f00ff"}, {"name": "Card", "parent": "Dealer", "rotation": 360, "scaleX": 0.75, "scaleY": 0.75}, {"name": "Card Position", "parent": "Card"}, {"name": "Bubble Talk", "parent": "Dealer", "x": 52, "y": 134}, {"name": "Jewelry 1", "parent": "Body", "x": 81.84}, {"name": "Jewelry 2", "parent": "Hip", "rotation": 90, "x": 30.63, "y": 22.88}, {"name": "Jewelry 3", "parent": "Hip", "rotation": 90, "x": 27.53, "y": 13.76, "scaleX": 0.75, "scaleY": 0.75}, {"name": "Jewelry 4", "parent": "Hip", "rotation": 90, "x": 17.47, "y": 14.14, "scaleX": 0.55, "scaleY": 0.55}, {"name": "Card Hand Left", "parent": "Hand Left", "rotation": 71.3, "x": 28.35, "y": -8.05, "scaleX": 0.75, "scaleY": 0.75}], "slots": [{"name": "Card Trash", "bone": "Dealer", "attachment": "Card Trash"}, {"name": "Card Box", "bone": "Dealer", "attachment": "Card Box"}, {"name": "Card 6", "bone": "Card Position"}, {"name": "Card 7", "bone": "Card Hand Left"}, {"name": "Card 8", "bone": "Card Hand Left"}, {"name": "Card 9", "bone": "Card Hand Left"}, {"name": "Card 5", "bone": "Card Position"}, {"name": "Card 4", "bone": "Card Position"}, {"name": "Card 3", "bone": "Card Position"}, {"name": "Card 2", "bone": "Card Position"}, {"name": "Card 1", "bone": "Card Position"}, {"name": "<PERSON><PERSON><PERSON> Right Shadow", "bone": "Forearm Right", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON> Left Shadow", "bone": "<PERSON>earm Left", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "Arm Left", "bone": "Arm Left", "attachment": "Arm"}, {"name": "Arm Right", "bone": "Arm Right", "attachment": "Arm"}, {"name": "Dealer_Mask", "bone": "Dealer", "attachment": "Dealer_Mask"}, {"name": "Body", "bone": "Body", "attachment": "Body"}, {"name": "Jewelry2", "bone": "Jewelry 2", "attachment": "Jewelry"}, {"name": "Jewelry3", "bone": "Jewelry 3", "attachment": "Jewelry"}, {"name": "Jewelry4", "bone": "Jewelry 4", "attachment": "Jewelry"}, {"name": "Jewelry 1", "bone": "Jewelry 1", "attachment": "Jewelry"}, {"name": "Hair Back", "bone": "Head", "attachment": "Hair Back"}, {"name": "Head", "bone": "Head", "attachment": "Head"}, {"name": "Hair", "bone": "Hair Top", "attachment": "Hair"}, {"name": "<PERSON>earm Left", "bone": "<PERSON>earm Left", "attachment": "Forearm 1"}, {"name": "Forearm Right", "bone": "Forearm Right", "attachment": "Forearm 1"}, {"name": "Forearm 6", "bone": "<PERSON>earm Left"}, {"name": "Bubble Invite Bet", "bone": "Bubble Talk"}, {"name": "Bubble Stop Bet", "bone": "Bubble Talk"}], "ik": [{"name": "<PERSON><PERSON><PERSON> Left IK", "order": 28, "bones": ["Arm Left", "<PERSON>earm Left"], "target": "<PERSON><PERSON><PERSON> Left IK", "bendPositive": false}, {"name": "Forearm Right IK", "order": 29, "bones": ["Arm Right", "Forearm Right"], "target": "Forearm Right IK"}], "transform": [{"name": "Arm  Left 3D", "order": 18, "bones": ["Arm Left"], "target": "Body 3D Vertical", "rotation": 25.36, "x": 14.63, "y": 150.77, "rotateMix": 0, "translateMix": -0.02, "scaleMix": 0, "shearMix": 0}, {"name": "Arm Left Bend Over", "order": 25, "bones": ["Arm Left"], "target": "Body Bend Over", "rotation": -64.64, "x": 188, "y": 18.09, "rotateMix": 0, "translateMix": 0.025, "scaleMix": 0, "shearMix": 0}, {"name": "Arm Right 3D", "order": 19, "bones": ["Arm Right"], "target": "Body 3D Vertical", "rotation": -114.64, "x": -34.7, "y": 161.85, "rotateMix": 0, "translateMix": -0.02, "scaleMix": 0, "shearMix": 0}, {"name": "Arm Right Bend Over", "order": 26, "bones": ["Arm Right"], "target": "Body Bend Over", "rotation": -114.64, "x": 113.43, "y": 17.05, "rotateMix": 0, "translateMix": 0.025, "scaleMix": 0, "shearMix": 0}, {"name": "Body Negative", "order": 15, "bones": ["Body Negative"], "target": "Body 3D Vertical", "y": -100, "rotateMix": 0, "translateMix": -0.25, "scaleMix": 0, "shearMix": 0}, {"name": "Body Positive", "order": 14, "bones": ["Body Positive"], "target": "Body 3D Vertical", "y": -50, "rotateMix": 0, "translateMix": 0.25, "scaleMix": 0, "shearMix": 0}, {"name": "Card Hand Left", "order": 30, "bones": ["Card"], "target": "Hand Left", "rotation": 100, "x": 24, "y": -7.5, "scaleX": -0.25, "scaleY": -0.25, "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "Card hand Right", "order": 31, "bones": ["Card"], "target": "Hand Right", "rotation": 100, "x": 40, "y": 7.5, "scaleX": -0.25, "scaleY": -0.25, "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "Chest Bend Over", "order": 1, "bones": ["Chest"], "target": "Body Bend Over", "rotation": 90.36, "x": 149.64, "y": -14.77, "shearY": -360, "rotateMix": 0, "translateMix": 0.25, "scaleMix": 0, "shearMix": 0}, {"name": "<PERSON><PERSON><PERSON> Left Bend Over", "order": 16, "bones": ["<PERSON><PERSON><PERSON>"], "target": "Body Bend Over", "rotation": 25.36, "x": 162.82, "y": 6.17, "rotateMix": 0, "translateMix": 0.03, "scaleMix": 0, "shearMix": 0}, {"name": "<PERSON><PERSON><PERSON> Right Bend Over", "order": 17, "bones": ["Clavicle Right"], "target": "Body Bend Over", "rotation": 155.36, "x": 136.19, "y": 6, "shearY": -360, "rotateMix": 0, "translateMix": 0.03, "scaleMix": 0, "shearMix": 0}, {"name": "Hair Left 3 Bend Over", "order": 23, "bones": ["Hair Left 3"], "target": "Body Bend Over", "rotation": -90.58, "x": 170.39, "y": 27.49, "rotateMix": 0, "translateMix": -0.05, "scaleMix": 0, "shearMix": 0}, {"name": "Hair Left 3 3D", "order": 24, "bones": ["Hair Left 3"], "target": "Body 3D Vertical", "rotation": -90.85, "x": 20.56, "y": 181.34, "rotateMix": 0, "translateMix": 0.0025, "scaleMix": 0, "shearMix": 0}, {"name": "Hair Left Head Rotation", "order": 22, "bones": ["Hair Left Head Rotation"], "target": "Head", "local": true, "x": -14, "rotateMix": -0.35, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "Hair Right 3 Bend Over", "order": 21, "bones": ["Hair Right 3"], "target": "Body Bend Over", "rotation": -79.98, "x": 117.68, "y": 28.34, "rotateMix": 0, "translateMix": -0.035, "scaleMix": 0, "shearMix": 0}, {"name": "Hair Right 3 3D", "order": 27, "bones": ["Hair Right 3"], "target": "Body 3D Vertical", "rotation": -80.25, "x": -32.13, "y": 182.48, "rotateMix": 0, "translateMix": 0.0025, "scaleMix": 0, "shearMix": 0}, {"name": "Hair Right Head Rotation", "order": 20, "bones": ["Hair Right Head Rotation"], "target": "Head", "local": true, "x": -14, "rotateMix": -0.35, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "Head 3D Bend Over", "order": 3, "bones": ["Head 3D Bend Over"], "target": "Body Bend Over", "rotation": 90, "x": 150, "y": 150, "rotateMix": 0, "translateMix": 4, "scaleMix": 0, "shearMix": 0}, {"name": "Head Negative", "order": 13, "bones": ["Head Negative"], "target": "Head 3D Vertical", "x": 100, "rotateMix": 0, "translateMix": -0.25, "scaleMix": 0, "shearMix": 0}, {"name": "Head Positive", "order": 4, "bones": ["Head Positive"], "target": "Head 3D Vertical", "x": 50, "rotateMix": 0, "translateMix": 0.25, "scaleMix": 0, "shearMix": 0}, {"name": "Hip Bend Over", "order": 0, "bones": ["Body"], "target": "Hip Bend Over", "rotation": 90.36, "x": -150, "y": -71.84, "shearY": -360, "rotateMix": 0, "translateMix": 0.25, "scaleMix": 0, "shearMix": 0}, {"name": "Jewelry 1 Bend Over", "order": 11, "bones": ["Jewelry 1"], "target": "Body Bend Over", "rotation": 90.36, "x": 149.48, "y": 10, "shearY": -360, "rotateMix": 0, "translateMix": 0.145, "scaleMix": 0, "shearMix": 0}, {"name": "Jewelry 2 Bend Over", "order": 10, "bones": ["Jewelry 2"], "target": "Hip Bend Over", "rotation": 90, "x": -119.37, "y": -77.12, "shearY": -360, "rotateMix": 0, "translateMix": 0.075, "scaleMix": 0, "shearMix": 0}, {"name": "Jewelry 3 Bend Over", "order": 9, "bones": ["Jewelry 3"], "target": "Hip Bend Over", "rotation": 90, "x": -122.47, "y": -86.24, "scaleX": -0.25, "scaleY": -0.25, "shearY": -360, "rotateMix": 0, "translateMix": 0.05, "scaleMix": 0, "shearMix": 0}, {"name": "Jewelry 4 Bend Over", "order": 8, "bones": ["Jewelry 4"], "target": "Hip Bend Over", "rotation": 90, "x": -132.53, "y": -85.86, "scaleX": -0.45, "scaleY": -0.45, "shearY": -360, "rotateMix": 0, "translateMix": 0.04, "scaleMix": 0, "shearMix": 0}, {"name": "Jewelry 1 3D", "order": 12, "bones": ["Jewelry 1"], "target": "Body 3D Vertical", "rotation": 90, "y": 160, "shearY": -360, "rotateMix": 0, "translateMix": 0.01, "scaleMix": 0, "shearMix": 0}, {"name": "Jewelry 2 3D", "order": 5, "bones": ["Jewelry 2"], "target": "Body 3D Vertical", "rotation": 90, "x": 30.63, "y": 72.88, "shearY": -360, "rotateMix": 0, "translateMix": 0.0025, "scaleMix": 0, "shearMix": 0}, {"name": "Jewelry 3 3D", "order": 6, "bones": ["Jewelry 3"], "target": "Body 3D Vertical", "rotation": 90, "x": 27.53, "y": 63.76, "scaleX": -0.25, "scaleY": -0.25, "shearY": -360, "rotateMix": 0, "translateMix": 0.005, "scaleMix": 0, "shearMix": 0}, {"name": "Jewelry 4 3D", "order": 7, "bones": ["Jewelry 4"], "target": "Body 3D Vertical", "rotation": 90, "x": 17.47, "y": 64.14, "scaleX": -0.45, "scaleY": -0.45, "shearY": -360, "rotateMix": 0, "translateMix": 0.01, "scaleMix": 0, "shearMix": 0}, {"name": "Neck Bend Over", "order": 2, "bones": ["Neck"], "target": "Body Bend Over", "rotation": 90.36, "x": 149.47, "y": 10.97, "shearY": -360, "rotateMix": 0, "translateMix": 0.175, "scaleMix": 0, "shearMix": 0}], "skins": {"default": {"Arm Left": {"Arm": {"type": "mesh", "uvs": [0.51611, 0, 0.22368, 0.0474, 0.05016, 0.14345, 0, 0.24463, 0.01416, 0.41895, 0.10712, 0.9286, 0.27791, 0.98111, 0.53191, 1, 0.79029, 0.97378, 0.88664, 0.89807, 1, 0.38185, 0.97422, 0.1791, 0.78153, 0.06309, 0.70338, 0.12363, 0.47893, 0.90026, 0.77754, 0.39022], "triangles": [7, 14, 8, 6, 14, 7, 13, 0, 12, 1, 0, 13, 2, 1, 13, 13, 12, 11, 3, 2, 13, 13, 11, 10, 15, 13, 10, 4, 3, 13, 4, 13, 15, 9, 15, 10, 14, 4, 15, 14, 15, 9, 5, 4, 14, 8, 14, 9, 6, 5, 14], "vertices": [3, 11, -4.9, -4.19, 0.46875, 9, 22.48, 5.04, 0.25, 4, 34.92, -31.56, 0.28125, 3, 11, 0.03, -12.67, 0.298, 9, 14, 0.11, 0.447, 4, 26.87, -25.95, 0.255, 3, 11, 10.02, -17.7, 0.35, 9, 8.96, -9.88, 0.35, 4, 15.69, -25.61, 0.3, 3, 11, 20.54, -19.16, 0.6, 9, 7.51, -20.4, 0.2, 4, 5.54, -28.74, 0.2, 2, 11, 38.67, -18.75, 0.85, 4, -10.72, -36.77, 0.15, 1, 11, 91.67, -16.05, 1, 1, 11, 97.13, -11.1, 1, 1, 11, 99.1, -3.73, 1, 1, 11, 96.37, 3.76, 1, 1, 11, 88.5, 6.55, 1, 2, 11, 34.81, 9.84, 0.9, 4, 4.86, -61.06, 0.1, 2, 11, 13.73, 9.09, 0.8, 4, 23.66, -51.47, 0.2, 2, 11, 1.66, 3.51, 0.8, 4, 32.23, -41.31, 0.2, 2, 11, 7.96, 1.24, 0.75, 4, 25.57, -41.91, 0.25, 1, 11, 88.73, -5.27, 1, 2, 11, 35.71, 3.23, 0.8, 4, 2.08, -56.6, 0.2], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 0, 24, 18, 20, 20, 22, 8, 10, 6, 8, 26, 4, 26, 6, 26, 8, 26, 20, 26, 22, 26, 24, 26, 0, 26, 2, 8, 30, 30, 20, 26, 30], "width": 29, "height": 104}}, "Arm Right": {"Arm": {"type": "mesh", "uvs": [0.51611, 0, 0.22368, 0.0474, 0.05016, 0.14345, 0, 0.24463, 0.01416, 0.41895, 0.10712, 0.9286, 0.27791, 0.98111, 0.53191, 1, 0.79029, 0.97378, 0.88664, 0.89807, 1, 0.38185, 0.97422, 0.1791, 0.78153, 0.06309, 0.70338, 0.12363, 0.47893, 0.90026, 0.76864, 0.39055], "triangles": [13, 0, 12, 1, 0, 13, 2, 1, 13, 13, 12, 11, 3, 2, 13, 13, 11, 10, 15, 13, 10, 4, 3, 13, 4, 13, 15, 9, 15, 10, 14, 4, 15, 14, 15, 9, 5, 4, 14, 8, 14, 9, 6, 5, 14, 7, 14, 8, 6, 14, 7], "vertices": [3, 12, -4.28, 2.85, 0.46875, 10, 22.48, -5.04, 0.25, 4, 34.92, 31.56, 0.28125, 3, 12, 0.65, 11.33, 0.298, 10, 14, -0.11, 0.447, 4, 26.87, 25.95, 0.255, 3, 12, 10.64, 16.36, 0.35, 10, 8.96, 9.88, 0.35, 4, 15.69, 25.61, 0.3, 3, 12, 21.17, 17.82, 0.6, 10, 7.51, 20.4, 0.2, 4, 5.54, 28.74, 0.2, 2, 12, 39.3, 17.41, 0.85, 4, -10.72, 36.77, 0.15, 1, 12, 92.3, 14.71, 1, 1, 12, 97.76, 9.76, 1, 1, 12, 99.72, 2.39, 1, 1, 12, 97, -5.1, 1, 1, 12, 89.12, -7.9, 1, 2, 12, 35.44, -11.18, 0.9, 4, 4.86, 61.06, 0.1, 2, 12, 14.35, -10.44, 0.8, 4, 23.66, 51.47, 0.2, 2, 12, 2.29, -4.85, 0.8, 4, 32.23, 41.31, 0.2, 2, 12, 8.58, -2.58, 0.75, 4, 25.57, 41.91, 0.25, 1, 12, 89.35, 3.93, 1, 2, 12, 36.37, -4.46, 0.8, 4, 1.44, 55.41, 0.2], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 0, 24, 18, 20, 20, 22, 8, 10, 6, 8, 26, 4, 26, 6, 26, 8, 26, 20, 26, 22, 26, 24, 26, 0, 26, 2, 8, 30, 30, 20, 26, 30], "width": 29, "height": 104}}, "Body": {"Body": {"type": "mesh", "uvs": [0, 0.96112, 0.22955, 0.996, 0.50138, 0.99999, 0.76863, 1, 1, 0.97028, 0.90524, 0.83625, 0.85719, 0.79833, 0.86581, 0.73122, 0.95845, 0.67295, 1, 0.59302, 1, 0.49259, 0.91789, 0.39328, 0.92437, 0.30159, 0.92695, 0.20789, 0.93068, 0.12784, 0.75451, 0.07859, 0.67466, 0.03499, 0.67466, 0, 0.50757, 0, 0.32886, 0, 0.32872, 0.0366, 0.24327, 0.0773, 0.07597, 0.12735, 0.0816, 0.21906, 0.07944, 0.30814, 0.07597, 0.39685, 0, 0.49032, 0, 0.58479, 0.04016, 0.67683, 0.13648, 0.72851, 0.15192, 0.79887, 0.10376, 0.84005, 0.29388, 0.74776, 0.50086, 0.72954, 0.71723, 0.74776, 0.50166, 0.79262, 0.50343, 0.86582, 0.26805, 0.79675, 0.24564, 0.84943, 0.7372, 0.79644, 0.75417, 0.8471, 0.50181, 0.45755, 0.80481, 0.28099, 0.17767, 0.27201, 0.50181, 0.6401, 0.49696, 0.31241, 0.17552, 0.57935, 0.83929, 0.57915, 0.49643, 0.15225, 0.39176, 0.10674, 0.60824, 0.10864, 0.33885, 0.16647, 0.49645, 0.22241, 0.66629, 0.16742, 0.81199, 0.16189, 0.18268, 0.16418, 0.37474, 0.32067, 0.63195, 0.32301, 0.85183, 0.6588, 0.15428, 0.65767], "triangles": [3, 5, 4, 0, 31, 1, 51, 49, 48, 53, 50, 16, 51, 20, 49, 48, 18, 50, 48, 49, 18, 50, 18, 16, 49, 20, 18, 18, 20, 19, 18, 17, 16, 13, 54, 14, 23, 22, 55, 46, 43, 56, 57, 42, 47, 57, 53, 42, 53, 57, 52, 56, 43, 51, 56, 52, 45, 56, 51, 52, 57, 45, 52, 52, 51, 48, 52, 50, 53, 52, 48, 50, 53, 15, 54, 55, 21, 51, 11, 42, 12, 12, 42, 13, 42, 54, 13, 42, 53, 54, 54, 15, 14, 25, 24, 43, 43, 55, 51, 24, 23, 43, 43, 23, 55, 55, 22, 21, 3, 2, 40, 2, 36, 40, 3, 40, 5, 1, 38, 2, 2, 38, 36, 1, 31, 38, 36, 39, 40, 38, 37, 36, 37, 35, 36, 36, 35, 39, 31, 30, 38, 38, 30, 37, 40, 6, 5, 40, 39, 6, 30, 29, 37, 6, 39, 7, 37, 32, 35, 37, 29, 32, 35, 34, 39, 39, 34, 7, 32, 33, 35, 35, 33, 34, 7, 34, 58, 29, 59, 32, 33, 32, 46, 58, 34, 47, 7, 58, 8, 44, 33, 46, 47, 34, 33, 59, 46, 32, 29, 28, 59, 59, 28, 46, 9, 8, 47, 33, 44, 47, 8, 58, 47, 46, 28, 27, 46, 41, 44, 44, 41, 47, 47, 10, 9, 27, 26, 46, 26, 25, 46, 46, 56, 41, 46, 25, 43, 41, 57, 47, 47, 11, 10, 47, 42, 11, 56, 45, 41, 41, 45, 57, 53, 16, 15, 21, 20, 51], "vertices": [1, 1, -43.13, 5.48, 1, 2, 2, -23.39, 0.77, 0.5, 1, -23.39, 0.77, 0.5, 2, 2, -0.01, 0.23, 0.75, 1, -0.02, 0.23, 0.25, 2, 2, 22.97, 0.23, 0.5, 1, 22.97, 0.22, 0.5, 1, 1, 42.87, 4.24, 1, 3, 3, -5.83, -34.72, 0.24125, 2, 34.72, 22.33, 0.72375, 18, 34.73, 172.39, 0.035, 3, 3, -0.71, -30.59, 0.475, 2, 30.59, 27.45, 0.475, 18, 30.59, 177.55, 0.05, 3, 6, -20.49, -2.33, 0.2925, 3, 8.35, -31.33, 0.6825, 18, 31.28, 186.71, 0.025, 2, 6, -12.62, -10.3, 0.3, 3, 16.22, -39.3, 0.7, 2, 6, -1.83, -13.87, 0.35, 3, 27.01, -42.87, 0.65, 3, 6, 11.73, -13.87, 0.24375, 3, 40.57, -42.87, 0.73125, 18, 42.61, 219, 0.025, 3, 3, 53.98, -35.81, 0.7275, 4, -3.09, -35.81, 0.2425, 18, 35.47, 232.36, 0.03, 3, 9, 15.95, -20.55, 0.48, 4, 8.98, -36.45, 0.32, 11, 20.76, -10.76, 0.2, 3, 9, 21.36, -9.55, 0.188, 4, 21.24, -36.71, 0.248, 11, 9.77, -5.36, 0.564, 2, 4, 32.74, -36.91, 0.2, 11, -0.66, -0.26, 0.8, 5, 3, 96.46, -21.76, 0.27166, 9, 15.49, 13.23, 0.27166, 4, 39.39, -21.76, 0.3008, 8, 13.62, -21.76, 0.09588, 18, 21.14, 274.75, 0.06, 4, 3, 102.34, -14.89, 0.35188, 4, 45.28, -14.89, 0.10531, 8, 19.5, -14.89, 0.52781, 18, 14.24, 280.59, 0.015, 4, 3, 107.07, -14.89, 0.28663, 4, 50, -14.89, 0.41175, 8, 24.23, -14.89, 0.28663, 18, 14.21, 285.31, 0.015, 4, 3, 107.07, -0.52, 0.27845, 4, 50, -0.52, 0.4, 8, 24.23, -0.52, 0.27845, 17, -0.16, 235.22, 0.0431, 4, 3, 107.07, 14.85, 0.28663, 4, 50, 14.85, 0.41175, 8, 24.23, 14.85, 0.28663, 18, -15.53, 285.12, 0.015, 4, 3, 102.13, 14.86, 0.35188, 4, 45.06, 14.86, 0.10531, 8, 19.29, 14.86, 0.52781, 18, -15.51, 280.18, 0.015, 5, 3, 96.63, 22.21, 0.27166, 10, 15.97, -13.19, 0.27166, 4, 39.57, 22.21, 0.3008, 8, 13.79, 22.21, 0.09588, 18, -22.82, 274.65, 0.06, 2, 4, 32.81, 36.6, 0.2, 12, -0.23, -0.82, 0.8, 3, 10, 20.35, 10.38, 0.188, 4, 20.06, 36.14, 0.248, 12, 11.17, 4.99, 0.564, 3, 10, 15.43, 21.41, 0.48, 4, 7.98, 36.34, 0.32, 12, 22.21, 9.92, 0.2, 3, 3, 53.49, 36.6, 0.7275, 4, -3.57, 36.6, 0.2425, 18, -36.94, 231.42, 0.03, 3, 7, 12.03, 15.13, 0.24375, 3, 40.87, 43.13, 0.73125, 18, -43.39, 218.76, 0.025, 2, 7, -0.72, 15.13, 0.35, 3, 28.12, 43.13, 0.65, 2, 7, -13.14, 11.68, 0.3, 3, 15.7, 39.68, 0.7, 3, 7, -20.12, 3.39, 0.29235, 3, 8.72, 31.39, 0.68215, 18, -31.45, 186.68, 0.0255, 3, 3, -0.78, 30.06, 0.475, 2, -30.06, 27.38, 0.475, 18, -30.06, 177.28, 0.05, 3, 3, -6.34, 34.21, 0.24125, 2, -34.21, 21.82, 0.72375, 18, -34.2, 171.77, 0.035, 3, 7, -22.72, -10.14, 0.27692, 3, 6.12, 17.86, 0.6, 17, -17.89, 134.17, 0.12308, 4, 7, -20.26, -27.94, 0.15741, 6, -20.26, 29.06, 0.15741, 3, 8.58, 0.06, 0.5, 17, -0.11, 136.74, 0.18519, 3, 6, -22.72, 10.45, 0.27692, 3, 6.12, -18.55, 0.6, 17, 18.51, 134.4, 0.12308, 3, 3, 0.06, -0.01, 0.36, 2, 0.01, 28.23, 0.54, 17, 0.01, 128.23, 0.1, 3, 3, -9.82, -0.17, 0.135, 2, 0.17, 18.34, 0.765, 17, 0.18, 118.34, 0.1, 3, 3, -0.49, 20.08, 0.4275, 2, -20.08, 27.67, 0.5225, 17, -20.08, 127.61, 0.05, 3, 3, -7.6, 22, 0.19, 2, -22, 20.56, 0.76, 17, -21.99, 120.53, 0.05, 3, 3, -0.45, -20.27, 0.4275, 2, 20.27, 27.71, 0.5225, 17, 20.27, 127.77, 0.05, 3, 3, -7.29, -21.73, 0.19, 2, 21.73, 20.87, 0.76, 17, 21.74, 120.9, 0.05, 3, 3, 45.3, -0.03, 0.4625, 4, -11.77, -0.03, 0.4625, 17, -0.26, 173.46, 0.075, 3, 9, 7.86, -13.37, 0.588, 4, 12.07, -26.08, 0.392, 18, 25.64, 247.46, 0.02, 3, 10, 9.97, 13.01, 0.588, 4, 13.28, 27.85, 0.392, 18, -28.3, 248.33, 0.02, 4, 7, -8.18, -28.03, 0.24, 6, -8.18, 28.97, 0.24, 3, 20.66, -0.03, 0.32, 17, -0.11, 148.81, 0.2, 3, 3, 64.9, 0.39, 0.475, 4, 7.83, 0.39, 0.475, 17, -0.8, 193.05, 0.05, 3, 7, 0.02, 0.03, 0.52, 3, 28.86, 28.03, 0.28, 17, -28.22, 156.84, 0.2, 3, 6, 0.04, -0.05, 0.52, 3, 28.88, -29.05, 0.28, 17, 28.87, 157.23, 0.2, 4, 3, 86.52, 0.44, 0.14667, 4, 29.45, 0.44, 0.18665, 8, 3.68, 0.44, 0.58668, 17, -0.99, 214.67, 0.08, 4, 3, 92.66, 9.44, 0.20111, 4, 35.59, 9.44, 0.30963, 8, 9.82, 9.44, 0.46926, 17, -10.03, 220.75, 0.02, 4, 3, 92.4, -9.18, 0.20111, 4, 35.34, -9.18, 0.30963, 8, 9.56, -9.18, 0.46926, 17, 8.59, 220.61, 0.02, 4, 3, 84.59, 13.99, 0.45554, 4, 27.53, 13.99, 0.4, 8, 1.75, 13.99, 0.11388, 18, -14.53, 262.66, 0.03058, 3, 3, 77.05, 0.43, 0.38, 4, 19.98, 0.43, 0.57, 17, -0.92, 205.2, 0.05, 4, 3, 84.47, -14.17, 0.45554, 4, 27.4, -14.17, 0.4, 8, 1.63, -14.17, 0.11388, 18, 13.63, 262.71, 0.03058, 3, 9, 15.21, 0.94, 0.585, 4, 28.15, -26.7, 0.35, 18, 26.16, 263.54, 0.065, 3, 10, 15.73, -0.36, 0.585, 4, 27.84, 27.42, 0.35, 18, -27.96, 262.89, 0.065, 3, 3, 63.78, 10.9, 0.47, 4, 6.71, 10.9, 0.47, 17, -11.31, 191.87, 0.06, 3, 3, 63.46, -11.22, 0.47, 4, 6.4, -11.22, 0.47, 17, 10.81, 191.69, 0.06, 3, 6, -10.71, -1.13, 0.3825, 3, 18.13, -30.13, 0.4675, 17, 30.01, 146.48, 0.15, 3, 7, -10.56, 1.86, 0.3825, 3, 18.28, 29.86, 0.4675, 17, -29.98, 146.25, 0.15], "hull": 32, "edges": [0, 2, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 0, 62, 2, 4, 4, 6, 70, 66, 4, 72, 72, 70, 60, 74, 74, 70, 62, 76, 76, 72, 12, 78, 78, 70, 10, 80, 80, 72, 22, 84, 50, 86, 66, 88, 88, 82, 58, 64, 64, 66, 66, 68, 68, 14, 42, 102, 102, 104, 104, 106, 106, 30, 112, 82, 82, 114, 82, 94, 94, 22, 92, 82, 92, 50, 92, 56, 92, 64, 94, 88, 88, 92, 92, 52, 92, 54, 94, 20, 94, 18, 94, 68, 92, 66, 66, 94, 86, 112, 114, 84, 102, 112, 106, 114, 86, 92, 92, 112, 114, 94, 94, 84], "width": 86, "height": 135}}, "Bubble Invite Bet": {"Bubble Invite Bet": {"x": 84.6, "y": 22.99, "width": 175, "height": 49}}, "Bubble Stop Bet": {"Bubble Stop Bet": {"x": 84.6, "y": 22.99, "width": 175, "height": 49}}, "Card 1": {"Card": {"width": 36, "height": 51}}, "Card 2": {"Card": {"x": 3.74, "y": -5.1, "width": 36, "height": 51}}, "Card 3": {"Card": {"x": 8.16, "y": 1.02, "width": 36, "height": 51}}, "Card 4": {"Card": {"x": -4.17, "y": -8.16, "width": 36, "height": 51}}, "Card 5": {"Card": {"x": 0.67, "y": -15.86, "width": 36, "height": 51}}, "Card 6": {"Card": {"x": 7.14, "y": -13.26, "width": 36, "height": 51}}, "Card 7": {"Card": {"width": 36, "height": 51}}, "Card 8": {"Card": {"x": 5.41, "y": 9.51, "width": 36, "height": 51}}, "Card 9": {"Card": {"x": -0.17, "y": 15.83, "width": 36, "height": 51}}, "Card Box": {"Card Box": {"x": 198.01, "y": -2.8, "width": 116, "height": 57}}, "Card Trash": {"Card Trash": {"x": -179.76, "y": -2.64, "width": 79, "height": 55}}, "Dealer_Mask": {"Dealer_Mask": {"type": "clipping", "end": "Jewelry 1", "vertexCount": 3, "vertices": [-105.37, 22.27, 126.77, 22.6, 0.02, 349.87], "color": "ce3a3aff"}}, "Forearm 6": {"Forearm 6": {"type": "mesh", "uvs": [0.16837, 0.47346, 0, 0.83049, 0, 0.89993, 0.0674, 0.96085, 0.21114, 1, 0.37718, 0.95737, 0.52491, 0.86846, 0.49893, 0.48943, 0.49673, 0.45734, 0.56174, 0.43496, 0.822, 0.33991, 0.82305, 0.269, 0.81616, 0.18546, 1, 0.08389, 0.84695, 0, 0.62079, 0.07101, 0.45137, 0.12677, 0.25156, 0.24727, 0.16956, 0.41442, 0.18204, 0.44446, 0.20942, 0.90243], "triangles": [12, 15, 14, 12, 14, 13, 15, 12, 11, 16, 15, 11, 17, 16, 10, 10, 16, 11, 18, 17, 9, 9, 17, 10, 9, 8, 18, 3, 20, 4, 4, 20, 5, 3, 2, 20, 5, 20, 6, 2, 1, 20, 1, 0, 20, 20, 7, 6, 20, 0, 7, 0, 19, 7, 19, 8, 7, 8, 19, 18], "vertices": [2, 13, 56.71, 5.92, 0.99073, 42, -8.4, 6.57, 0.00927, 1, 13, 9.51, 10.37, 1, 1, 13, 0.44, 9.73, 1, 1, 13, -7.31, 6.08, 1, 1, 13, -11.96, -0.87, 1, 1, 13, -5.85, -8.1, 1, 1, 13, 6.24, -14.06, 1, 1, 13, 55.69, -9.4, 1, 1, 13, 59.87, -9, 1, 1, 13, 63.01, -11.78, 1, 1, 42, 8.38, -22.2, 1, 1, 42, 16.34, -21.6, 1, 1, 42, 25.69, -20.52, 1, 1, 42, 37.59, -28.02, 1, 1, 42, 46.58, -20.23, 1, 1, 42, 37.99, -10.5, 1, 1, 42, 31.27, -3.24, 1, 1, 42, 17.2, 4.82, 1, 1, 42, -1.78, 7.05, 1, 2, 13, 60.54, 5.56, 0.5, 42, -5.11, 6.21, 0.5, 1, 13, 0.79, 0.1, 1], "hull": 20, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 4, 2, 2, 0, 0, 38, 12, 14, 14, 16, 14, 0, 18, 36, 38, 16, 40, 4, 40, 12, 40, 10, 40, 8, 40, 6, 20, 34, 24, 30, 20, 22, 22, 24, 22, 32], "width": 46, "height": 131}}, "Forearm Left": {"Forearm 1": {"type": "mesh", "uvs": [0.75862, 0.02815, 0.59195, 0.00826, 0.42684, 0.03145, 0.37869, 0.10839, 0.40162, 0.56599, 0.39302, 0.59538, 0.36264, 0.61875, 0.24138, 0.68003, 0.24138, 0.82841, 0.30087, 0.82863, 0.29745, 0.87943, 0.29378, 0.93385, 0.28987, 0.99174, 0.56505, 0.99174, 0.60218, 0.92374, 0.63342, 0.86795, 0.67078, 0.80402, 0.68486, 0.63095, 0.68486, 0.60393, 0.68486, 0.57703, 0.75862, 0.11813, 0.60028, 0.08966], "triangles": [13, 11, 14, 12, 11, 13, 14, 10, 15, 11, 10, 14, 15, 9, 16, 10, 9, 15, 6, 5, 17, 7, 6, 17, 16, 7, 17, 9, 8, 7, 16, 9, 7, 17, 5, 18, 21, 1, 0, 21, 0, 20, 21, 4, 3, 19, 21, 20, 2, 1, 21, 21, 3, 2, 4, 21, 19, 18, 4, 19, 5, 4, 18], "vertices": [2, 11, 92.18, 4.76, 0.25, 13, -3.32, 10.59, 0.75, 2, 11, 85.92, -2.98, 0.3, 13, -5.39, 0.85, 0.7, 2, 11, 84.41, -12.84, 0.18, 13, -2.25, -8.62, 0.82, 1, 13, 7.15, -11.09, 1, 1, 13, 62.44, -7.83, 1, 2, 13, 66.01, -8.2, 0.5, 42, 0.39, -7.83, 0.5, 1, 42, 3.39, -9.27, 1, 1, 42, 11.53, -15.45, 1, 1, 42, 29.38, -13.49, 1, 2, 42, 29.03, -10.06, 0.5, 43, 2.64, -10.02, 0.5, 2, 43, 8.76, -9.46, 0.5, 44, 2.06, -9.47, 0.5, 2, 44, 8.63, -8.88, 0.5, 45, 1.85, -8.88, 0.5, 1, 45, 8.83, -8.23, 1, 1, 45, 6.85, 7.6, 1, 2, 44, 5.25, 8.72, 0.5, 45, -1.58, 8.72, 0.5, 2, 43, 4.98, 9.7, 0.5, 44, -1.67, 9.71, 0.5, 2, 42, 23.72, 10.94, 0.5, 43, -2.96, 10.9, 0.5, 2, 13, 69.72, 8.86, 0.5, 42, 2.82, 9.46, 0.5, 2, 13, 66.46, 8.75, 0.75, 42, -0.43, 9.11, 0.25, 1, 13, 63.2, 8.63, 1, 1, 13, 7.56, 10.97, 1, 1, 13, 4.44, 1.67, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 16, 18, 24, 26, 38, 40, 0, 40, 34, 36, 36, 38, 12, 34, 10, 36, 8, 38, 18, 32, 22, 24, 26, 28, 22, 28, 18, 20, 20, 22, 28, 30, 30, 32, 20, 30, 14, 16, 32, 34, 14, 32, 14, 34], "width": 58, "height": 121}, "Forearm 2": {"type": "mesh", "uvs": [0.75862, 0.02815, 0.59195, 0.00826, 0.42684, 0.03145, 0.37869, 0.10839, 0.40162, 0.56599, 0.39302, 0.59538, 0.36264, 0.61875, 0.24138, 0.68003, 0.19129, 0.89551, 0.64583, 0.86473, 0.79641, 0.79328, 0.68486, 0.63095, 0.68486, 0.60393, 0.68486, 0.57703, 0.75862, 0.11813, 0.60028, 0.08966], "triangles": [8, 7, 9, 9, 7, 10, 7, 11, 10, 7, 6, 11, 6, 5, 11, 11, 5, 12, 5, 4, 12, 12, 4, 13, 4, 15, 13, 13, 15, 14, 15, 3, 2, 15, 4, 3, 15, 0, 14, 2, 1, 15, 15, 1, 0], "vertices": [2, 11, 92.18, 4.76, 0.25, 13, -3.32, 10.59, 0.75, 2, 11, 85.92, -2.98, 0.3, 13, -5.39, 0.85, 0.7, 2, 11, 84.41, -12.84, 0.18, 13, -2.25, -8.62, 0.82, 1, 13, 7.15, -11.09, 1, 1, 13, 62.44, -7.83, 1, 2, 13, 66.01, -8.2, 0.5, 42, 0.39, -7.83, 0.5, 1, 42, 3.39, -9.27, 1, 1, 42, 11.53, -15.45, 1, 1, 42, 37.71, -15.53, 1, 1, 42, 31.18, 10.29, 1, 1, 42, 21.65, 18.01, 1, 2, 13, 69.72, 8.86, 0.5, 42, 2.82, 9.46, 0.5, 2, 13, 66.46, 8.75, 0.75, 42, -0.43, 9.11, 0.25, 1, 13, 63.2, 8.63, 1, 1, 13, 7.56, 10.97, 1, 1, 13, 4.44, 1.67, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 26, 28, 0, 28, 22, 24, 24, 26, 12, 22, 10, 24, 8, 26, 18, 20, 14, 16, 20, 22, 14, 20, 14, 22, 16, 18], "width": 58, "height": 121}, "Forearm 3": {"type": "mesh", "uvs": [0.75862, 0.02197, 0.59195, 0.00209, 0.42684, 0.02527, 0.37869, 0.10221, 0.40162, 0.55981, 0.39302, 0.58921, 0.36264, 0.61257, 0.24138, 0.67385, 0.10178, 0.77127, 0.16198, 0.80318, 0.33377, 0.74528, 0.28896, 0.95177, 0.38629, 0.95551, 0.44149, 0.81615, 0.42607, 0.99638, 0.53292, 1, 0.55146, 0.83266, 0.5813, 0.98824, 0.67273, 0.98101, 0.65018, 0.82809, 0.70025, 0.94993, 0.77113, 0.94224, 0.74502, 0.81341, 0.68486, 0.62478, 0.68486, 0.59775, 0.68486, 0.57085, 0.75862, 0.11195, 0.60028, 0.08349], "triangles": [16, 13, 6, 19, 16, 23, 11, 10, 13, 12, 11, 13, 17, 16, 19, 17, 19, 18, 15, 14, 13, 16, 15, 13, 20, 19, 22, 20, 22, 21, 6, 5, 23, 10, 7, 6, 9, 8, 7, 9, 7, 10, 6, 23, 16, 10, 6, 13, 22, 19, 23, 27, 1, 0, 2, 1, 27, 27, 0, 26, 27, 4, 3, 27, 3, 2, 25, 27, 26, 4, 27, 25, 24, 4, 25, 5, 4, 24, 23, 5, 24], "vertices": [2, 11, 92.18, 4.76, 0.25, 13, -3.32, 10.59, 0.75, 2, 11, 85.92, -2.98, 0.3, 13, -5.39, 0.85, 0.7, 2, 11, 84.41, -12.84, 0.18, 13, -2.25, -8.62, 0.82, 1, 13, 7.15, -11.09, 1, 1, 13, 62.44, -7.83, 1, 2, 13, 66.01, -8.2, 0.5, 42, 0.39, -7.83, 0.5, 1, 42, 3.39, -9.27, 1, 1, 42, 11.53, -15.45, 1, 1, 42, 23.97, -22.26, 1, 1, 42, 27.45, -18.36, 1, 2, 43, -6.65, -9.33, 0.5, 44, -13.35, -9.34, 0.5, 1, 45, 4.81, -8.8, 1, 1, 45, 4.57, -3.14, 1, 1, 45, -12.43, -2.07, 1, 1, 45, 9.16, -0.23, 1, 1, 45, 8.83, 5.97, 1, 2, 44, -4.46, 4.51, 0.15259, 45, -11.27, 4.51, 0.84741, 2, 44, 13.88, 8.58, 0.23349, 45, 7.08, 8.58, 0.76651, 2, 44, 12.37, 13.74, 0.5, 45, 5.53, 13.73, 0.5, 3, 43, 0.97, 10.13, 0.09735, 44, -5.71, 10.13, 0.5, 45, -12.51, 10.13, 0.40265, 3, 43, 15.1, 14.86, 0.20183, 44, 8.42, 14.86, 0.5, 45, 1.62, 14.86, 0.29817, 2, 43, 13.64, 18.85, 0.5, 44, 6.98, 18.85, 0.5, 2, 42, 25.14, 15.42, 0.5, 43, -1.54, 15.37, 0.5, 2, 13, 69.72, 8.86, 0.5, 42, 2.82, 9.46, 0.5, 2, 13, 66.46, 8.75, 0.75, 42, -0.43, 9.11, 0.25, 1, 13, 63.2, 8.63, 1, 1, 13, 7.56, 10.97, 1, 1, 13, 4.44, 1.67, 1], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 50, 52, 0, 52, 46, 48, 48, 50, 12, 46, 10, 48, 8, 50, 42, 44, 44, 46, 20, 22, 14, 16, 16, 18, 22, 24, 28, 30, 24, 26, 26, 28, 30, 32, 32, 34, 34, 36, 40, 42, 36, 38, 38, 40, 18, 20], "width": 58, "height": 121}, "Forearm 4": {"type": "mesh", "uvs": [0.75862, 0.02815, 0.59195, 0.00826, 0.42684, 0.03145, 0.37869, 0.10839, 0.40162, 0.56599, 0.39302, 0.59538, 0.36264, 0.61875, 0.27421, 0.67783, 0.36006, 0.98446, 0.48789, 0.98645, 0.74593, 0.71656, 0.68486, 0.63095, 0.68486, 0.60393, 0.68486, 0.57703, 0.75862, 0.11813, 0.60028, 0.08966], "triangles": [6, 5, 11, 8, 7, 6, 9, 8, 6, 11, 9, 6, 10, 9, 11, 15, 1, 0, 2, 1, 15, 15, 0, 14, 15, 4, 3, 15, 3, 2, 13, 15, 14, 4, 15, 13, 12, 4, 13, 5, 4, 12, 11, 5, 12], "vertices": [2, 11, 92.18, 4.76, 0.25, 13, -3.32, 10.59, 0.75, 2, 11, 85.92, -2.98, 0.3, 13, -5.39, 0.85, 0.7, 2, 11, 84.41, -12.84, 0.18, 13, -2.25, -8.62, 0.82, 1, 13, 7.15, -11.09, 1, 1, 13, 62.44, -7.83, 1, 2, 13, 66.01, -8.2, 0.5, 42, 0.39, -7.83, 0.5, 1, 42, 3.39, -9.27, 1, 1, 42, 11.05, -13.59, 1, 1, 42, 47.14, -4.65, 1, 1, 42, 46.67, 2.76, 1, 1, 42, 12.78, 14.14, 1, 2, 13, 69.72, 8.86, 0.5, 42, 2.82, 9.46, 0.5, 2, 13, 66.46, 8.75, 0.75, 42, -0.43, 9.11, 0.25, 1, 13, 63.2, 8.63, 1, 1, 13, 7.56, 10.97, 1, 1, 13, 4.44, 1.67, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 16, 18, 26, 28, 0, 28, 22, 24, 24, 26, 12, 22, 10, 24, 8, 26, 18, 20, 14, 16, 20, 22], "width": 58, "height": 121}, "Forearm 5": {"type": "mesh", "uvs": [0.75862, 0.01243, 0.59195, 0, 0.42684, 0.01573, 0.37869, 0.09267, 0.40162, 0.55027, 0.39302, 0.57966, 0.36264, 0.60303, 0.31652, 0.68396, 0.3489, 0.95789, 0.50135, 1, 0.86061, 0.81401, 0.68486, 0.61523, 0.68486, 0.5882, 0.68486, 0.56131, 0.75862, 0.10241, 0.60028, 0.07394], "triangles": [11, 9, 7, 9, 11, 10, 11, 7, 6, 9, 8, 7, 6, 5, 11, 11, 5, 12, 5, 4, 12, 12, 4, 13, 4, 15, 13, 15, 3, 2, 2, 1, 15, 13, 15, 14, 15, 4, 3, 15, 0, 14, 15, 1, 0], "vertices": [2, 11, 92.18, 4.76, 0.25, 13, -3.32, 10.59, 0.75, 2, 11, 85.92, -2.98, 0.3, 13, -5.39, 0.85, 0.7, 2, 11, 84.41, -12.84, 0.18, 13, -2.25, -8.62, 0.82, 1, 13, 7.15, -11.09, 1, 1, 13, 62.44, -7.83, 1, 2, 13, 66.01, -8.2, 0.5, 42, 0.39, -7.83, 0.5, 1, 42, 3.39, -9.27, 1, 1, 42, 13.41, -10.87, 1, 1, 42, 46.04, -5.47, 1, 1, 42, 50.26, 4.03, 1, 1, 42, 25.59, 22.17, 1, 2, 13, 69.72, 8.86, 0.5, 42, 2.82, 9.46, 0.5, 2, 13, 66.46, 8.75, 0.75, 42, -0.43, 9.11, 0.25, 1, 13, 63.2, 8.63, 1, 1, 13, 7.56, 10.97, 1, 1, 13, 4.44, 1.67, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 16, 18, 26, 28, 0, 28, 22, 24, 24, 26, 12, 22, 10, 24, 8, 26, 20, 22, 14, 16, 18, 20], "width": 58, "height": 121}}, "Forearm Left Shadow": {"Forearm Shadow": {"type": "mesh", "color": "00000064", "uvs": [0.70833, 0.12189, 0.57407, 0.10596, 0.44107, 0.12454, 0.40228, 0.18619, 0.42075, 0.55288, 0.41382, 0.57643, 0.38935, 0.59516, 0.29167, 0.64426, 0.29167, 0.76317, 0.33959, 0.76334, 0.33683, 0.80405, 0.33388, 0.84765, 0.33073, 0.89404, 0.5524, 0.89404, 0.58231, 0.83955, 0.60748, 0.79485, 0.63757, 0.74362, 0.64891, 0.60494, 0.64891, 0.58328, 0.64891, 0.56172, 0.70833, 0.194, 0.58078, 0.17119], "triangles": [13, 11, 14, 12, 11, 13, 14, 10, 15, 11, 10, 14, 15, 9, 16, 10, 9, 15, 6, 5, 17, 7, 6, 17, 16, 7, 17, 9, 8, 7, 16, 9, 7, 17, 5, 18, 21, 1, 0, 21, 0, 20, 21, 4, 3, 19, 21, 20, 2, 1, 21, 21, 3, 2, 4, 21, 19, 18, 4, 19, 5, 4, 18], "vertices": [2, 11, 92.2, 5.74, 0.25, 13, -4.2, 11.22, 0.75, 2, 11, 86.26, -2.48, 0.3, 13, -5.59, 1.43, 0.7, 2, 11, 85.31, -12.54, 0.18, 13, -1.86, -7.74, 0.82, 1, 13, 7.69, -9.6, 1, 1, 13, 62.62, -2.56, 1, 2, 13, 66.22, -2.66, 0.5, 42, 0.57, -2.28, 0.5, 1, 42, 3.66, -3.45, 1, 1, 42, 12.14, -8.97, 1, 1, 42, 29.83, -5.59, 1, 2, 42, 29.28, -2.19, 0.5, 43, 2.89, -2.15, 0.5, 2, 43, 8.96, -1.1, 0.5, 44, 2.27, -1.1, 0.5, 2, 44, 8.78, 0.01, 0.5, 45, 2, 0.01, 0.5, 1, 45, 8.92, 1.21, 1, 1, 45, 6.02, 16.85, 1, 2, 44, 4.37, 17.3, 0.5, 45, -2.46, 17.3, 0.5, 2, 43, 4.06, 17.73, 0.5, 44, -2.59, 17.73, 0.5, 2, 42, 22.75, 18.33, 0.5, 43, -3.93, 18.29, 0.5, 2, 13, 68.75, 14.59, 0.5, 42, 1.98, 15.19, 0.5, 2, 13, 65.5, 14.24, 0.75, 42, -1.25, 14.6, 0.25, 1, 13, 62.26, 13.92, 1, 1, 13, 6.58, 12.44, 1, 1, 13, 4.11, 2.95, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 16, 18, 24, 26, 38, 40, 0, 40, 34, 36, 36, 38, 12, 34, 10, 36, 8, 38, 18, 32, 22, 24, 26, 28, 22, 28, 18, 20, 20, 22, 28, 30, 30, 32, 20, 30, 14, 16, 32, 34, 14, 32, 14, 34], "width": 72, "height": 151}}, "Forearm Right": {"Forearm 1": {"type": "mesh", "uvs": [0.75862, 0.02815, 0.59195, 0.00826, 0.42684, 0.03145, 0.37869, 0.10839, 0.40162, 0.56599, 0.39302, 0.59538, 0.36264, 0.61875, 0.24138, 0.68003, 0.24138, 0.82841, 0.30087, 0.82863, 0.29745, 0.87943, 0.29378, 0.93385, 0.28987, 0.99174, 0.56505, 0.99174, 0.60218, 0.92374, 0.63342, 0.86795, 0.67078, 0.80402, 0.68486, 0.63095, 0.68486, 0.60393, 0.68486, 0.57703, 0.75862, 0.11813, 0.60028, 0.08966], "triangles": [12, 11, 13, 13, 11, 14, 11, 10, 14, 14, 10, 15, 10, 9, 15, 15, 9, 16, 16, 9, 7, 9, 8, 7, 16, 7, 17, 7, 6, 17, 6, 5, 17, 17, 5, 18, 5, 4, 18, 18, 4, 19, 4, 21, 19, 19, 21, 20, 21, 3, 2, 21, 4, 3, 21, 0, 20, 2, 1, 21, 21, 1, 0], "vertices": [2, 12, 92.42, -6.29, 0.25, 14, -3.18, -10.6, 0.75, 2, 12, 86.15, 1.46, 0.3, 14, -5.24, -0.85, 0.7, 2, 12, 84.64, 11.32, 0.18, 14, -2.11, 8.62, 0.82, 1, 14, 7.3, 11.08, 1, 1, 14, 62.59, 7.82, 1, 2, 14, 66.16, 8.2, 0.5, 47, 0.39, 7.55, 0.5, 1, 47, 3.4, 8.97, 1, 1, 47, 11.6, 15.07, 1, 1, 47, 29.43, 12.95, 1, 2, 47, 29.04, 9.52, 0.5, 48, 2.38, 9.56, 0.5, 2, 48, 8.51, 9.11, 0.5, 49, 1.86, 9.11, 0.5, 2, 49, 8.43, 8.62, 0.5, 50, 1.6, 8.63, 0.5, 1, 50, 8.59, 8.13, 1, 1, 50, 6.94, -7.75, 1, 2, 49, 5.31, -9.03, 0.5, 50, -1.47, -9.04, 0.5, 2, 48, 5.1, -10.12, 0.5, 49, -1.6, -10.12, 0.5, 2, 47, 23.55, -11.43, 0.5, 48, -2.82, -11.47, 0.5, 2, 14, 69.87, -8.87, 0.5, 47, 2.66, -9.77, 0.5, 2, 14, 66.6, -8.75, 0.75, 47, -0.59, -9.38, 0.25, 1, 14, 63.35, -8.64, 1, 1, 14, 7.7, -10.98, 1, 1, 14, 4.58, -1.68, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 16, 18, 24, 26, 38, 40, 0, 40, 34, 36, 36, 38, 12, 34, 10, 36, 8, 38, 18, 32, 22, 24, 26, 28, 22, 28, 18, 20, 20, 22, 28, 30, 30, 32, 20, 30, 32, 34, 14, 16, 32, 14, 14, 34], "width": 58, "height": 121}, "Forearm 2": {"type": "mesh", "uvs": [0.75862, 0.02815, 0.59195, 0.00826, 0.42684, 0.03145, 0.37869, 0.10839, 0.40162, 0.56599, 0.39302, 0.59538, 0.36264, 0.61875, 0.24138, 0.68003, 0.18543, 0.88788, 0.63342, 0.86795, 0.80291, 0.79262, 0.68486, 0.63095, 0.68486, 0.60393, 0.68486, 0.57703, 0.75862, 0.11813, 0.60028, 0.08966], "triangles": [6, 5, 11, 7, 6, 11, 7, 11, 10, 9, 7, 10, 8, 7, 9, 15, 1, 0, 2, 1, 15, 15, 0, 14, 15, 4, 3, 15, 3, 2, 13, 15, 14, 4, 15, 13, 12, 4, 13, 5, 4, 12, 11, 5, 12], "vertices": [2, 12, 92.42, -6.29, 0.25, 14, -3.18, -10.6, 0.75, 2, 12, 86.15, 1.46, 0.3, 14, -5.24, -0.85, 0.7, 2, 12, 84.64, 11.32, 0.18, 14, -2.11, 8.62, 0.82, 1, 14, 7.3, 11.08, 1, 1, 14, 62.59, 7.82, 1, 2, 14, 66.16, 8.2, 0.5, 47, 0.39, 7.55, 0.5, 1, 47, 3.4, 8.97, 1, 1, 47, 11.6, 15.07, 1, 1, 47, 36.87, 15.34, 1, 1, 47, 31.49, -10.19, 1, 1, 47, 21.32, -18.84, 1, 2, 14, 69.87, -8.87, 0.5, 47, 2.66, -9.77, 0.5, 2, 14, 66.6, -8.75, 0.75, 47, -0.59, -9.38, 0.25, 1, 14, 63.35, -8.64, 1, 1, 14, 7.7, -10.98, 1, 1, 14, 4.58, -1.68, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 26, 28, 0, 28, 22, 24, 24, 26, 12, 22, 10, 24, 8, 26, 18, 20, 16, 18, 20, 22, 20, 14, 14, 22, 14, 16], "width": 58, "height": 121}, "Forearm 3": {"type": "mesh", "uvs": [0.75862, 0.01827, 0.59195, 0, 0.42684, 0.02157, 0.37869, 0.09851, 0.40162, 0.55611, 0.39302, 0.58551, 0.36264, 0.60887, 0.08822, 0.7687, 0.18562, 0.79788, 0.32626, 0.74366, 0.29569, 0.94566, 0.37868, 0.95361, 0.44567, 0.81818, 0.43823, 0.99402, 0.53277, 1, 0.55232, 0.82376, 0.58543, 0.98275, 0.6718, 0.98106, 0.65329, 0.82631, 0.69212, 0.94968, 0.76947, 0.94247, 0.68486, 0.62107, 0.68486, 0.59405, 0.68486, 0.56715, 0.75862, 0.10825, 0.60028, 0.07978], "triangles": [15, 14, 12, 14, 13, 12, 16, 18, 17, 16, 15, 18, 11, 10, 12, 19, 18, 20, 10, 9, 12, 18, 21, 20, 18, 15, 21, 21, 15, 6, 9, 6, 12, 15, 12, 6, 9, 8, 6, 6, 8, 7, 6, 5, 21, 21, 5, 22, 5, 4, 22, 22, 4, 23, 4, 25, 23, 23, 25, 24, 25, 3, 2, 25, 4, 3, 25, 0, 24, 2, 1, 25, 25, 1, 0], "vertices": [2, 12, 92.42, -6.29, 0.25, 14, -3.18, -10.6, 0.75, 2, 12, 86.15, 1.46, 0.3, 14, -5.24, -0.85, 0.7, 2, 12, 84.64, 11.32, 0.18, 14, -2.11, 8.62, 0.82, 1, 14, 7.3, 11.08, 1, 1, 14, 62.59, 7.82, 1, 2, 14, 66.16, 8.2, 0.5, 47, 0.39, 7.55, 0.5, 1, 47, 3.4, 8.97, 1, 1, 47, 24.42, 22.5, 1, 1, 47, 27.27, 16.47, 1, 1, 47, 19.84, 9.14, 1, 1, 47, 44.24, 8.03, 1, 1, 47, 44.63, 3.13, 1, 1, 47, 27.96, 1.2, 1, 1, 47, 49.08, -0.87, 1, 1, 47, 49.16, -6.41, 1, 1, 47, 27.92, -5.01, 1, 1, 47, 46.74, -9.19, 1, 1, 47, 45.96, -14.14, 1, 1, 47, 27.55, -10.86, 1, 1, 47, 42.07, -14.86, 1, 1, 47, 40.68, -19.21, 1, 2, 14, 69.87, -8.87, 0.5, 47, 2.66, -9.77, 0.5, 2, 14, 66.6, -8.75, 0.75, 47, -0.59, -9.38, 0.25, 1, 14, 63.35, -8.64, 1, 1, 14, 7.7, -10.98, 1, 1, 14, 4.58, -1.68, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 46, 48, 0, 48, 42, 44, 44, 46, 12, 42, 10, 44, 8, 46, 38, 40, 36, 38, 34, 36, 32, 34, 26, 28, 18, 20, 14, 16, 16, 18, 28, 30, 30, 32, 20, 22, 22, 24, 24, 26, 40, 42, 12, 14], "width": 58, "height": 121}, "Forearm 4": {"type": "mesh", "uvs": [0.75862, 0.02815, 0.59195, 0.00826, 0.42684, 0.03145, 0.37869, 0.10839, 0.40162, 0.56599, 0.39302, 0.59538, 0.36264, 0.61875, 0.26759, 0.68774, 0.35848, 0.98084, 0.49902, 0.98543, 0.74699, 0.70637, 0.68486, 0.63095, 0.68486, 0.60393, 0.68486, 0.57703, 0.75862, 0.11813, 0.60028, 0.08966], "triangles": [6, 5, 11, 8, 7, 6, 9, 8, 6, 11, 9, 6, 10, 9, 11, 15, 1, 0, 2, 1, 15, 15, 0, 14, 15, 4, 3, 15, 3, 2, 13, 15, 14, 4, 15, 13, 12, 4, 13, 5, 4, 12, 11, 5, 12], "vertices": [2, 12, 92.42, -6.29, 0.25, 14, -3.18, -10.6, 0.75, 2, 12, 86.15, 1.46, 0.3, 14, -5.24, -0.85, 0.7, 2, 12, 84.64, 11.32, 0.18, 14, -2.11, 8.62, 0.82, 1, 14, 7.3, 11.08, 1, 1, 14, 62.59, 7.82, 1, 2, 14, 66.16, 8.2, 0.5, 47, 0.39, 7.55, 0.5, 1, 47, 3.4, 8.97, 1, 1, 47, 12.33, 13.47, 1, 1, 47, 47.08, 3.87, 1, 1, 47, 46.53, -4.18, 1, 1, 47, 11.38, -14.5, 1, 2, 14, 69.87, -8.87, 0.5, 47, 2.66, -9.77, 0.5, 2, 14, 66.6, -8.75, 0.75, 47, -0.59, -9.38, 0.25, 1, 14, 63.35, -8.64, 1, 1, 14, 7.7, -10.98, 1, 1, 14, 4.58, -1.68, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 26, 28, 0, 28, 22, 24, 24, 26, 12, 22, 10, 24, 8, 26, 20, 22, 14, 16, 16, 18, 18, 20], "width": 58, "height": 121}, "Forearm 5": {"type": "mesh", "uvs": [0.75862, 0.00783, 0.59195, 0, 0.42684, 0.01113, 0.37869, 0.08807, 0.40162, 0.54567, 0.39302, 0.57506, 0.36264, 0.59843, 0.31064, 0.67711, 0.35049, 0.95805, 0.50805, 1, 0.84923, 0.80093, 0.68486, 0.61063, 0.68486, 0.58361, 0.68486, 0.55671, 0.75862, 0.09781, 0.60028, 0.06934], "triangles": [10, 9, 11, 11, 9, 7, 11, 7, 6, 7, 9, 8, 6, 5, 11, 11, 5, 12, 5, 4, 12, 12, 4, 13, 4, 15, 13, 15, 3, 2, 2, 1, 15, 13, 15, 14, 15, 4, 3, 15, 0, 14, 15, 1, 0], "vertices": [2, 12, 92.42, -6.29, 0.25, 14, -3.18, -10.6, 0.75, 2, 12, 86.15, 1.46, 0.3, 14, -5.24, -0.85, 0.7, 2, 12, 84.64, 11.32, 0.18, 14, -2.11, 8.62, 0.82, 1, 14, 7.3, 11.08, 1, 1, 14, 62.59, 7.82, 1, 2, 14, 66.16, 8.2, 0.5, 47, 0.39, 7.55, 0.5, 1, 47, 3.4, 8.97, 1, 1, 47, 13.2, 10.87, 1, 1, 47, 46.78, 4.5, 1, 1, 47, 50.63, -5.19, 1, 1, 47, 24.47, -22.01, 1, 2, 14, 69.87, -8.87, 0.5, 47, 2.66, -9.77, 0.5, 2, 14, 66.6, -8.75, 0.75, 47, -0.59, -9.38, 0.25, 1, 14, 63.35, -8.64, 1, 1, 14, 7.7, -10.98, 1, 1, 14, 4.58, -1.68, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 26, 28, 0, 28, 22, 24, 24, 26, 12, 22, 10, 24, 8, 26, 20, 22, 14, 16, 16, 18, 18, 20], "width": 58, "height": 121}}, "Forearm Right Shadow": {"Forearm Shadow": {"type": "mesh", "color": "00000064", "uvs": [0.70833, 0.12189, 0.57407, 0.10596, 0.44107, 0.12454, 0.40228, 0.18619, 0.42075, 0.55288, 0.41382, 0.57643, 0.38935, 0.59516, 0.29167, 0.64426, 0.29167, 0.76317, 0.33959, 0.76334, 0.33683, 0.80405, 0.33388, 0.84765, 0.33073, 0.89404, 0.5524, 0.89404, 0.58231, 0.83955, 0.60748, 0.79485, 0.63757, 0.74362, 0.64891, 0.60494, 0.64891, 0.58328, 0.64891, 0.56172, 0.70833, 0.194, 0.58078, 0.17119], "triangles": [12, 11, 13, 13, 11, 14, 11, 10, 14, 14, 10, 15, 10, 9, 15, 15, 9, 16, 16, 9, 7, 9, 8, 7, 16, 7, 17, 7, 6, 17, 6, 5, 17, 17, 5, 18, 5, 4, 18, 18, 4, 19, 4, 21, 19, 19, 21, 20, 21, 3, 2, 21, 4, 3, 21, 0, 20, 2, 1, 21, 21, 1, 0], "vertices": [2, 12, 92.72, -7.1, 0.25, 14, -3.65, -11.38, 0.75, 2, 12, 86.8, 1.1, 0.3, 14, -5.04, -1.58, 0.7, 2, 12, 85.85, 11.14, 0.18, 14, -1.31, 7.6, 0.82, 1, 14, 8.24, 9.47, 1, 1, 14, 63.18, 2.47, 1, 2, 14, 66.77, 2.58, 0.5, 47, 0.91, 1.95, 0.5, 1, 47, 4, 3.1, 1, 1, 47, 12.54, 8.55, 1, 1, 47, 30.2, 5.04, 1, 2, 47, 29.62, 1.65, 0.5, 48, 2.96, 1.68, 0.5, 2, 48, 9.05, 0.76, 0.5, 49, 2.4, 0.76, 0.5, 2, 49, 8.92, -0.24, 0.5, 50, 2.09, -0.24, 0.5, 1, 50, 9.03, -1.28, 1, 1, 50, 6.46, -16.99, 1, 2, 49, 4.78, -17.62, 0.5, 50, -2, -17.62, 0.5, 2, 48, 4.52, -18.16, 0.5, 49, -2.17, -18.15, 0.5, 2, 47, 22.91, -18.84, 0.5, 48, -3.46, -18.88, 0.5, 2, 14, 69.31, -14.67, 0.5, 47, 2.18, -15.54, 0.5, 2, 14, 66.06, -14.33, 0.75, 47, -1.05, -14.93, 0.25, 1, 14, 62.82, -14.01, 1, 1, 14, 7.15, -12.57, 1, 1, 14, 4.67, -3.08, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 16, 18, 24, 26, 38, 40, 0, 40, 34, 36, 36, 38, 12, 34, 10, 36, 8, 38, 18, 32, 22, 24, 26, 28, 22, 28, 18, 20, 20, 22, 28, 30, 30, 32, 20, 30, 32, 34, 14, 16, 32, 14, 14, 34], "width": 72, "height": 151}}, "Hair": {"Hair": {"type": "mesh", "uvs": [0.59785, 1e-05, 0.72405, 0.04581, 0.84103, 0.09408, 0.91425, 0.21878, 0.95618, 0.39776, 0.95034, 0.49277, 0.91245, 0.56951, 0.90534, 0.63868, 0.99999, 0.70049, 0.99999, 0.78921, 0.71099, 0.83451, 0.75546, 0.72568, 0.72485, 0.66035, 0.73178, 0.57962, 0.75451, 0.49798, 0.76031, 0.40661, 0.67498, 0.32417, 0.65297, 0.26581, 0.59785, 0.26361, 0.54273, 0.29991, 0.47412, 0.37922, 0.41568, 0.46388, 0.41858, 0.55556, 0.49203, 0.64471, 0.61963, 0.77844, 0.4324, 0.97598, 0.11602, 0.99998, 0.01359, 0.80713, 0.02067, 0.57992, 0.16695, 0.41411, 0.19304, 0.24057, 0.29561, 0.11148, 0.44254, 0.02497], "triangles": [10, 11, 9, 9, 11, 8, 11, 7, 8, 11, 12, 7, 12, 13, 7, 7, 13, 6, 13, 14, 6, 6, 14, 5, 14, 15, 5, 5, 15, 4, 24, 25, 26, 24, 26, 23, 23, 26, 27, 27, 22, 23, 27, 28, 22, 22, 29, 21, 22, 28, 29, 20, 21, 30, 21, 29, 30, 4, 15, 3, 15, 16, 3, 30, 31, 20, 20, 31, 19, 16, 17, 3, 31, 32, 19, 19, 32, 18, 17, 2, 3, 17, 1, 2, 17, 18, 1, 32, 0, 18, 18, 0, 1], "vertices": [2, 27, 15.71, -1.25, 0.97, 24, -149.9, -2.76, 0.03, 2, 27, 10.14, -12.7, 0.975, 24, -155.47, -14.21, 0.025, 2, 27, 4.28, -23.31, 0.98, 24, -161.33, -24.82, 0.02, 2, 27, -10.72, -29.88, 0.985, 24, -176.33, -31.38, 0.015, 2, 27, -32.23, -33.56, 0.5, 36, -1.2, 8.83, 0.5, 2, 36, 10.2, 9.19, 0.5, 37, -3.48, 8.64, 0.5, 2, 37, 6.35, 8.62, 0.5, 38, -0.62, 9.39, 0.5, 2, 38, 7.69, 8.83, 0.5, 39, 4.07, 7.98, 0.5, 2, 39, 15.14, 10.55, 0.5, 40, -2.75, 11.67, 0.5, 1, 40, 7.85, 12.67, 1, 1, 40, 15.74, -13, 1, 3, 39, 4.44, -9.19, 0.495, 40, 2.36, -10.2, 0.495, 23, -186.82, -15.6, 0.01, 3, 38, 10.47, -7.57, 0.49, 39, -3.52, -6.82, 0.49, 23, -178.98, -12.87, 0.02, 3, 37, 13.21, -6.37, 0.485, 38, 0.74, -7.04, 0.485, 23, -169.26, -13.56, 0.03, 3, 36, 12.21, -8.53, 0.48, 37, 3.33, -7.84, 0.48, 23, -159.5, -15.69, 0.04, 3, 27, -33.17, -15.73, 0.475, 36, 1.24, -8.85, 0.475, 23, -148.54, -16.29, 0.05, 2, 27, -23.23, -8.03, 0.94, 23, -138.6, -8.59, 0.06, 2, 27, -16.22, -6.07, 0.95, 23, -131.58, -6.63, 0.05, 2, 27, -15.92, -1.05, 0.94, 23, -131.29, -1.62, 0.06, 2, 27, -20.24, 3.99, 0.94, 23, -135.61, 3.43, 0.06, 3, 27, -29.72, 10.3, 0.4625, 30, 3.14, 14.13, 0.4625, 23, -145.09, 9.73, 0.075, 3, 30, 14.51, 12.69, 0.47, 31, -0.17, 12.9, 0.47, 23, -155.21, 15.12, 0.06, 3, 31, 10.37, 16.07, 0.48, 32, 1.67, 17.13, 0.48, 23, -166.22, 14.92, 0.04, 3, 32, 13.37, 21.85, 0.485, 33, -0.02, 22.54, 0.485, 23, -176.96, 8.31, 0.03, 2, 33, 19.38, 26.56, 0.98, 23, -193.08, -3.2, 0.02, 1, 33, 34.02, 1.3, 1, 1, 33, 24.85, -26.14, 1, 2, 32, 24.98, -24.42, 0.5, 33, -0.08, -25.16, 0.5, 2, 31, 22.8, -18.06, 0.5, 32, -1.76, -19.04, 0.5, 3, 30, 16.81, -10.61, 0.495, 31, 0.09, -10.51, 0.495, 24, -199.34, 36.77, 0.01, 3, 27, -12.92, 35.77, 0.49, 30, -3.53, -15.65, 0.49, 24, -178.53, 34.26, 0.02, 2, 27, 2.51, 26.33, 0.98, 24, -163.1, 24.83, 0.02, 2, 27, 12.81, 12.9, 0.975, 24, -152.81, 11.39, 0.025], "hull": 33, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 54, 56, 56, 58, 58, 60, 0, 64, 38, 40, 40, 42, 60, 62, 62, 64, 54, 46, 56, 44, 58, 42, 60, 40, 30, 8, 28, 10, 26, 12, 24, 14, 22, 16, 18, 20, 52, 54, 52, 48], "width": 91, "height": 120}}, "Hair Back": {"Hair Back": {"color": "230000ff", "x": 10, "scaleX": 0.85, "scaleY": 1.15, "rotation": -90, "width": 50, "height": 50}}, "Head": {"Head": {"type": "mesh", "uvs": [0.51429, 1e-05, 0.22666, 0, 0.05449, 0.09881, 0, 0.24881, 0, 0.5083, 0.04521, 0.60899, 0.09362, 0.7168, 0.19548, 0.82006, 0.2697, 0.8953, 0.41448, 1, 0.50022, 0.99949, 0.58732, 0.99897, 0.73535, 0.8923, 0.82885, 0.80195, 0.92317, 0.7108, 0.96326, 0.60281, 1, 0.5038, 1, 0.24431, 0.92709, 0.07481, 0.76665, 0, 0.5002, 0.3083, 0.50303, 0.67482, 0.50246, 0.74382, 0.62414, 0.69791, 0.37365, 0.70419, 0.2955, 0.75222, 0.50025, 0.77231, 0.6961, 0.74926, 0.5762, 0.89368, 0.4223, 0.89293, 0.80009, 0.63164, 0.19677, 0.64152, 0.10639, 0.4624, 0.18285, 0.52177, 0.30154, 0.52335, 0.36951, 0.50568, 0.30865, 0.45239, 0.19262, 0.44482, 0.07345, 0.34524, 0.2076, 0.36889, 0.40932, 0.42388, 0.44905, 0.35207, 0.22246, 0.30796, 0.5893, 0.42676, 0.55339, 0.35358, 0.77679, 0.31067, 0.92525, 0.33839, 0.79457, 0.36776, 0.19947, 0.40585, 0.31549, 0.41442, 0.41522, 0.49891, 0.30523, 0.55037, 0.16831, 0.5457, 0.06598, 0.4592, 0.62261, 0.49992, 0.69733, 0.45428, 0.8069, 0.45382, 0.87721, 0.46916, 0.81284, 0.52485, 0.69674, 0.52628, 0.79947, 0.40678, 0.69505, 0.41527, 0.57827, 0.4987, 0.69128, 0.55525, 0.81523, 0.55086, 0.92028, 0.46637, 0.77564, 0.16367, 0.22398, 0.15348, 0.12961, 0.26422, 0.87938, 0.25975, 0.77256, 0.73297, 0.58766, 0.92977, 0.41193, 0.93088, 0.21865, 0.72933, 0.31556, 0.83039, 0.68402, 0.82721], "triangles": [15, 65, 16, 5, 4, 53, 11, 10, 71, 9, 72, 10, 71, 10, 72, 11, 71, 12, 8, 72, 9, 72, 28, 71, 14, 30, 15, 6, 5, 31, 13, 70, 14, 7, 6, 73, 13, 75, 70, 74, 7, 73, 6, 31, 73, 70, 30, 14, 65, 46, 16, 5, 53, 52, 48, 53, 38, 53, 32, 52, 64, 65, 15, 29, 74, 25, 74, 73, 25, 67, 2, 1, 67, 1, 0, 66, 19, 18, 0, 19, 66, 69, 66, 18, 17, 69, 18, 68, 2, 67, 3, 2, 68, 20, 42, 68, 20, 67, 0, 20, 0, 66, 20, 68, 67, 20, 66, 69, 45, 20, 69, 46, 69, 17, 45, 69, 46, 38, 3, 68, 38, 68, 42, 41, 42, 20, 44, 20, 45, 47, 45, 46, 39, 38, 42, 48, 38, 39, 60, 47, 46, 42, 40, 39, 49, 39, 40, 48, 39, 49, 45, 43, 44, 47, 43, 45, 61, 47, 60, 41, 40, 42, 47, 61, 43, 36, 48, 49, 37, 48, 36, 60, 57, 56, 55, 61, 60, 55, 60, 56, 62, 43, 61, 48, 32, 53, 37, 32, 48, 46, 65, 60, 65, 57, 60, 20, 44, 62, 61, 54, 62, 41, 24, 50, 20, 24, 41, 49, 40, 50, 55, 54, 61, 35, 49, 50, 36, 49, 35, 33, 32, 37, 34, 37, 36, 34, 36, 35, 33, 37, 34, 58, 56, 57, 59, 54, 55, 59, 55, 56, 59, 56, 58, 52, 32, 33, 51, 34, 35, 51, 35, 50, 33, 34, 51, 52, 33, 51, 64, 58, 57, 64, 57, 65, 63, 54, 59, 62, 54, 63, 64, 63, 59, 64, 59, 58, 30, 63, 64, 31, 52, 51, 41, 50, 40, 62, 44, 43, 62, 23, 20, 21, 20, 23, 63, 23, 62, 23, 63, 30, 24, 51, 50, 20, 21, 24, 31, 51, 24, 73, 31, 24, 70, 23, 30, 22, 24, 21, 22, 21, 23, 27, 23, 70, 25, 73, 24, 75, 27, 70, 75, 28, 27, 26, 24, 22, 25, 24, 26, 26, 22, 23, 26, 23, 27, 27, 28, 26, 26, 29, 25, 29, 26, 28, 72, 74, 29, 30, 64, 15, 5, 52, 31, 53, 4, 38, 13, 12, 75, 8, 7, 74, 71, 28, 75, 72, 29, 28, 71, 75, 12, 8, 74, 72, 38, 4, 3, 46, 17, 16], "vertices": [2, 23, -124.87, -0.66, 0.52, 24, -174.87, -0.66, 0.48, 2, 23, -124.87, 12.57, 0.5, 24, -174.87, 12.57, 0.5, 2, 23, -130.8, 20.49, 0.496, 24, -180.8, 20.49, 0.504, 2, 23, -139.8, 23, 0.496, 24, -189.8, 23, 0.504, 2, 23, -155.37, 23, 0.4905, 24, -205.37, 23, 0.5095, 2, 23, -161.29, 21.41, 0.49315, 24, -211.53, 20.46, 0.50685, 2, 23, -167.88, 18.69, 0.498, 24, -217.88, 18.69, 0.502, 2, 23, -173.95, 14.49, 0.50063, 24, -224.2, 13.54, 0.49937, 2, 23, -178.59, 10.59, 0.502, 24, -228.59, 10.59, 0.498, 2, 23, -184.87, 3.93, 0.5, 24, -234.87, 3.93, 0.5, 2, 23, -188.1, 0.19, 0.506, 24, -231.59, -0.21, 0.494, 2, 23, -184.81, -4.02, 0.5, 24, -234.81, -4.02, 0.5, 2, 23, -178.41, -10.83, 0.502, 24, -228.41, -10.83, 0.498, 2, 23, -172.87, -14.65, 0.50002, 24, -223.11, -15.59, 0.49998, 2, 23, -167.52, -19.47, 0.498, 24, -217.52, -19.47, 0.502, 2, 23, -160.92, -20.82, 0.49313, 24, -211.16, -21.77, 0.50687, 2, 23, -155.1, -23, 0.4905, 24, -205.1, -23, 0.5095, 2, 23, -139.53, -23, 0.496, 24, -189.53, -23, 0.504, 2, 23, -129.36, -19.65, 0.496, 24, -179.36, -19.65, 0.504, 2, 23, -124.87, -12.27, 0.5, 24, -174.87, -12.27, 0.5, 2, 23, -143.37, -0.01, 0.5285, 24, -193.37, -0.01, 0.4715, 2, 23, -165.36, -0.14, 0.53, 24, -215.36, -0.14, 0.47, 2, 23, -169.5, -0.11, 0.526, 24, -219.5, -0.11, 0.474, 2, 23, -166.75, -5.71, 0.526, 24, -216.75, -5.71, 0.474, 2, 23, -167.13, 5.81, 0.526, 24, -217.13, 5.81, 0.474, 2, 23, -170.01, 9.41, 0.516, 24, -220.01, 9.41, 0.484, 2, 23, -171.21, -0.01, 0.524, 24, -221.21, -0.01, 0.476, 2, 23, -169.83, -9.02, 0.516, 24, -219.83, -9.02, 0.484, 2, 23, -178.49, -3.51, 0.516, 24, -228.49, -3.51, 0.484, 2, 23, -178.45, 3.57, 0.516, 24, -228.45, 3.57, 0.484, 2, 23, -162.77, -13.8, 0.52, 24, -212.77, -13.8, 0.48, 2, 23, -163.37, 13.95, 0.52, 24, -213.37, 13.95, 0.48, 2, 23, -152.62, 18.11, 0.52, 24, -202.62, 18.11, 0.48, 2, 23, -156.18, 14.59, 0.52, 24, -206.18, 14.59, 0.48, 2, 23, -156.28, 9.13, 0.52, 24, -206.28, 9.13, 0.48, 2, 23, -155.22, 6, 0.52, 24, -205.22, 6, 0.48, 2, 23, -152.02, 8.8, 0.528, 24, -202.02, 8.8, 0.472, 2, 23, -151.56, 14.14, 0.52, 24, -201.56, 14.14, 0.48, 2, 23, -145.59, 19.62, 0.528, 24, -195.59, 19.62, 0.472, 2, 23, -147.01, 13.45, 0.528, 24, -197.01, 13.45, 0.472, 2, 23, -150.31, 4.17, 0.528, 24, -200.31, 4.17, 0.472, 2, 23, -146, 2.34, 0.528, 24, -196, 2.34, 0.472, 2, 23, -143.35, 12.77, 0.528, 24, -193.35, 12.77, 0.472, 2, 23, -150.48, -4.11, 0.528, 24, -200.48, -4.11, 0.472, 2, 23, -146.09, -2.46, 0.528, 24, -196.09, -2.46, 0.472, 2, 23, -143.51, -12.73, 0.528, 24, -193.51, -12.73, 0.472, 2, 23, -145.18, -19.56, 0.528, 24, -195.18, -19.56, 0.472, 2, 23, -146.94, -13.55, 0.528, 24, -196.94, -13.55, 0.472, 2, 23, -149.23, 13.82, 0.52, 24, -199.23, 13.82, 0.48, 2, 23, -149.74, 8.49, 0.528, 24, -199.74, 8.49, 0.472, 2, 23, -154.81, 3.9, 0.528, 24, -204.81, 3.9, 0.472, 2, 23, -157.9, 8.96, 0.528, 24, -207.9, 8.96, 0.472, 2, 23, -157.62, 15.26, 0.52, 24, -207.62, 15.26, 0.48, 2, 23, -152.43, 19.96, 0.512, 24, -202.43, 19.96, 0.488, 2, 23, -154.87, -5.64, 0.52, 24, -204.87, -5.64, 0.48, 2, 23, -152.13, -9.08, 0.528, 24, -202.13, -9.08, 0.472, 2, 23, -152.1, -14.12, 0.52, 24, -202.1, -14.12, 0.48, 2, 23, -153.02, -17.35, 0.52, 24, -203.02, -17.35, 0.48, 2, 23, -156.37, -14.39, 0.52, 24, -206.37, -14.39, 0.48, 2, 23, -156.45, -9.05, 0.52, 24, -206.45, -9.05, 0.48, 2, 23, -149.28, -13.78, 0.52, 24, -199.28, -13.78, 0.48, 2, 23, -149.79, -8.97, 0.528, 24, -199.79, -8.97, 0.472, 2, 23, -154.8, -3.6, 0.528, 24, -204.8, -3.6, 0.472, 2, 23, -158.19, -8.8, 0.528, 24, -208.19, -8.8, 0.472, 2, 23, -157.93, -14.5, 0.52, 24, -207.93, -14.5, 0.48, 2, 23, -152.86, -19.33, 0.512, 24, -202.86, -19.33, 0.488, 2, 23, -134.69, -12.68, 0.52, 24, -184.69, -12.68, 0.48, 2, 23, -134.08, 12.7, 0.52, 24, -184.08, 12.7, 0.48, 2, 23, -140.73, 17.04, 0.52, 24, -190.73, 17.04, 0.48, 2, 23, -140.46, -17.45, 0.52, 24, -190.46, -17.45, 0.48, 2, 23, -168.85, -12.54, 0.512, 24, -218.85, -12.54, 0.488, 2, 23, -180.66, -4.03, 0.51, 24, -230.66, -4.03, 0.49, 2, 23, -180.73, 4.05, 0.51, 24, -230.73, 4.05, 0.49, 2, 23, -168.63, 12.94, 0.512, 24, -218.63, 12.94, 0.488, 2, 23, -174.58, 8.94, 0.518, 24, -224.83, 7.99, 0.482, 2, 23, -174.39, -8.01, 0.516, 24, -224.63, -8.96, 0.484], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 16, 18, 22, 24, 32, 34, 34, 36, 36, 38, 0, 38, 46, 40, 40, 48, 48, 44, 44, 46, 46, 42, 42, 48, 50, 52, 52, 54, 54, 56, 56, 58, 58, 50, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 64, 76, 78, 78, 80, 80, 82, 82, 84, 84, 76, 86, 88, 88, 90, 90, 92, 92, 94, 94, 86, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 96, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 108, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 120, 6, 136, 136, 40, 134, 136, 34, 138, 138, 40, 132, 138, 134, 0, 0, 132, 46, 140, 142, 144, 146, 48, 8, 10, 10, 12, 28, 30, 30, 32, 24, 26, 26, 28, 12, 14, 14, 16, 144, 148, 148, 146, 140, 150, 150, 142, 18, 20, 20, 22], "width": 46, "height": 60}}, "Jewelry 1": {"Jewelry": {"scaleX": 0.85, "scaleY": 0.85, "rotation": -90, "width": 10, "height": 10}}, "Jewelry2": {"Jewelry": {"rotation": -90, "width": 10, "height": 10}}, "Jewelry3": {"Jewelry": {"rotation": -90, "width": 10, "height": 10}}, "Jewelry4": {"Jewelry": {"rotation": -90, "width": 10, "height": 10}}}}, "animations": {"Discard": {"slots": {"Card 1": {"attachment": [{"time": 0.3333, "name": "Card"}, {"time": 1.5333, "name": null}]}, "Card 2": {"attachment": [{"time": 0.5, "name": "Card"}, {"time": 1.5333, "name": null}]}, "Card 3": {"attachment": [{"time": 0.6667, "name": "Card"}, {"time": 1.5333, "name": null}]}, "Card 4": {"attachment": [{"time": 1.1667, "name": "Card"}, {"time": 1.5333, "name": null}]}, "Card 5": {"attachment": [{"time": 1.1667, "name": "Card"}, {"time": 1.5333, "name": null}]}, "Card 6": {"attachment": [{"time": 1.1667, "name": "Card"}, {"time": 1.5333, "name": null}]}, "Card 7": {"attachment": [{"time": 0.3333, "name": "Card"}, {"time": 1, "name": null}]}, "Card 8": {"attachment": [{"time": 0.5, "name": "Card"}, {"time": 1, "name": null}]}, "Card 9": {"attachment": [{"time": 0.6667, "name": "Card"}, {"time": 1, "name": null}]}, "Forearm Left": {"attachment": [{"time": 0.3333, "name": "Forearm 3"}, {"time": 1, "name": "Forearm 2"}, {"time": 1.8333, "name": "Forearm 1"}]}, "Forearm Left Shadow": {"color": [{"time": 0, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffffff"}]}, "Forearm Right": {"attachment": [{"time": 0.3333, "name": "Forearm 3"}, {"time": 1.1667, "name": "Forearm 1"}, {"time": 1.5, "name": "Forearm 3"}, {"time": 1.7333, "name": "Forearm 1"}]}, "Forearm Right Shadow": {"color": [{"time": 0, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffffff"}]}}, "bones": {"Hand Left": {"rotate": [{"time": 0, "angle": -22}, {"time": 1, "angle": -18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -13.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -22}]}, "Forearm Right IK": {"translate": [{"time": 0, "x": 32.5, "y": 18}, {"time": 0.3333, "x": -47.16, "y": 5.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 28.14, "y": -3.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -105.8, "y": 41.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 32.5, "y": 18}]}, "Forearm Left IK": {"translate": [{"time": 0, "x": -32.5, "y": 18}, {"time": 0.3333, "x": 42.6, "y": 8.54}, {"time": 1, "x": -18.03, "y": -5.76}, {"time": 2, "x": -32.5, "y": 18}]}, "Finger Right 1": {"rotate": [{"time": 0, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Finger Right 2": {"rotate": [{"time": 0, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Finger Right 3": {"rotate": [{"time": 0, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Body Bend Over": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": -70.95, "y": 79.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -70.95, "y": -78.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 0}]}, "Forearm Right": {"rotate": [{"time": 0, "angle": 43.11, "curve": "stepped"}, {"time": 0.6667, "angle": 43.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -72.58, "curve": "stepped"}, {"time": 1.5, "angle": -72.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 43.11}]}, "Hand Right": {"rotate": [{"time": 0, "angle": 22}, {"time": 0.3333, "angle": 22.79, "curve": "stepped"}, {"time": 0.8333, "angle": 22.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 9.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 22}]}, "Body 3D Horizontal": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 0, "y": 73.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": -72.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0, "y": 148.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head 3D Horizontal": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": -0.14, "y": 22.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1.43, "y": -224.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 19.15, "y": 333.57, "curve": "stepped"}, {"time": 1.5, "x": 19.15, "y": 333.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Body": {"rotate": [{"time": 1, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 5.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Arm Right": {"rotate": [{"time": 0, "angle": 9.34, "curve": "stepped"}, {"time": 1, "angle": 9.34, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -4.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 9.34}]}, "Clavicle Right": {"rotate": [{"time": 1, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -11.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Finger Left 1": {"rotate": [{"time": 0, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 0, "curve": "stepped"}, {"time": 1.8333, "angle": -30, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Finger Left 3": {"rotate": [{"time": 0, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 0, "curve": "stepped"}, {"time": 1.8333, "angle": -30, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Finger Left 2": {"rotate": [{"time": 0, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 0, "curve": "stepped"}, {"time": 1.8333, "angle": -30, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Head": {"rotate": [{"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": -9.26, "curve": "stepped"}, {"time": 1.5, "angle": -9.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Card Position": {"rotate": [{"time": 1.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -29.25}], "translate": [{"time": 0, "x": 0.53, "y": 22.42, "curve": "stepped"}, {"time": 1.1667, "x": 0.53, "y": 22.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0.83, "y": 19.67}]}}, "ik": {"Forearm Left IK": [{"time": 1, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "mix": 0.35, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "bendPositive": false}], "Forearm Right IK": [{"time": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "mix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2}]}, "transform": {"Card hand Right": [{"time": 0, "scaleMix": 0, "shearMix": 0}]}, "deform": {"default": {"Head": {"Head": [{"time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "offset": 128, "vertices": [-1.44913, 0, -1.44913, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0, 0, 0, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.44913, 0, -1.44913, 0, 0, 0, 0, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -1.82721, 0, -1.82721, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.82721, 0, -1.82721], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "curve": "stepped"}, {"time": 1.1667, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "offset": 128, "vertices": [-1.44913, 0, -1.44913, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0, 0, 0, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.44913, 0, -1.44913, 0, 0, 0, 0, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -1.82721, 0, -1.82721, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.82721, 0, -1.82721], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5}]}}}}, "Idle": {"bones": {"Hand Left": {"rotate": [{"time": 0, "angle": -22}]}, "Hand Right": {"rotate": [{"time": 0, "angle": 22, "curve": "stepped"}, {"time": 1, "angle": 22, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 24.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 22}]}, "Forearm Right IK": {"translate": [{"time": 0, "x": 32.5, "y": 18}]}, "Forearm Left IK": {"translate": [{"time": 0, "x": -32.5, "y": 18}]}, "Finger Left 1": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Left 2": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Left 3": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Right 1": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 1, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -17.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Finger Right 2": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 1, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -17.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Finger Right 3": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 1, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -17.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Chest": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1.12, "y": 1.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "Breast": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 3.91, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Body 3D Vertical": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 25, "y": -0.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head 3D Vertical": {"translate": [{"time": 0, "x": 7.62, "y": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.5, "x": -100, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 115.25, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2, "x": 7.62, "y": 0}]}, "Body Bend Over": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 9.05, "y": -0.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}}}, "IdleBlink1": {"bones": {"Hand Left": {"rotate": [{"time": 0, "angle": -22}]}, "Hand Right": {"rotate": [{"time": 0, "angle": 22}]}, "Forearm Right IK": {"translate": [{"time": 0, "x": 32.5, "y": 18}]}, "Forearm Left IK": {"translate": [{"time": 0, "x": -32.5, "y": 18}]}, "Finger Left 1": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Left 2": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Left 3": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Right 1": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Right 2": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Right 3": {"rotate": [{"time": 0, "angle": -12}]}, "Chest": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1.12, "y": 1.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "Breast": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 3.91, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Body 3D Vertical": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 25, "y": -0.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head 3D Vertical": {"translate": [{"time": 0, "x": 7.62, "y": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.5, "x": -100, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 115.25, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2, "x": 7.62, "y": 0}]}, "Body Bend Over": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 9.05, "y": -0.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}}, "deform": {"default": {"Head": {"Head": [{"time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "offset": 128, "vertices": [-1.44913, 0, -1.44913, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0, 0, 0, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.44913, 0, -1.44913, 0, 0, 0, 0, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -1.82721, 0, -1.82721, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.82721, 0, -1.82721], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333}]}}}}, "IdleBlink2": {"bones": {"Hand Left": {"rotate": [{"time": 0, "angle": -22}]}, "Hand Right": {"rotate": [{"time": 0, "angle": 22}]}, "Forearm Right IK": {"translate": [{"time": 0, "x": 32.5, "y": 18}]}, "Forearm Left IK": {"translate": [{"time": 0, "x": -32.5, "y": 18}]}, "Finger Left 1": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Left 2": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Left 3": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Right 1": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Right 2": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Right 3": {"rotate": [{"time": 0, "angle": -12}]}, "Chest": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1.12, "y": 1.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "Breast": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 3.91, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Body 3D Vertical": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 25, "y": -0.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head 3D Vertical": {"translate": [{"time": 0, "x": 7.62, "y": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.5, "x": -100, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 115.25, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2, "x": 7.62, "y": 0}]}, "Body Bend Over": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 9.05, "y": -0.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}}, "deform": {"default": {"Head": {"Head": [{"time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "offset": 128, "vertices": [-1.44913, 0, -1.44913, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0, 0, 0, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.44913, 0, -1.44913, 0, 0, 0, 0, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -1.82721, 0, -1.82721, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.82721, 0, -1.82721], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "curve": "stepped"}, {"time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "offset": 128, "vertices": [-1.44913, 0, -1.44913, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0, 0, 0, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.44913, 0, -1.44913, 0, 0, 0, 0, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -1.82721, 0, -1.82721, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.82721, 0, -1.82721], "curve": [0.25, 0, 0.75, 1]}, {"time": 1}]}}}}, "IdleStatic": {"bones": {"Hand Left": {"rotate": [{"time": 0, "angle": -22, "curve": "stepped"}, {"time": 0.3333, "angle": -22}]}, "Hand Right": {"rotate": [{"time": 0, "angle": 22}]}, "Forearm Right IK": {"translate": [{"time": 0, "x": 32.5, "y": 18}]}, "Forearm Left IK": {"translate": [{"time": 0, "x": -32.5, "y": 18}]}, "Finger Left 1": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Left 2": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Left 3": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Right 1": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Right 2": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Right 3": {"rotate": [{"time": 0, "angle": -12}]}, "Head 3D Vertical": {"translate": [{"time": 0, "x": 7.62, "y": 0}]}}}, "IdleTiltLeft": {"bones": {"Hand Left": {"rotate": [{"time": 0, "angle": -22}]}, "Hand Right": {"rotate": [{"time": 0, "angle": 22}]}, "Forearm Right IK": {"translate": [{"time": 0, "x": 32.5, "y": 18}]}, "Forearm Left IK": {"translate": [{"time": 0, "x": -32.5, "y": 18}]}, "Finger Left 1": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Left 2": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Left 3": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Right 1": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Right 2": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Right 3": {"rotate": [{"time": 0, "angle": -12}]}, "Chest": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1.12, "y": 1.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "Breast": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 3.91, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Body 3D Vertical": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 25, "y": -0.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head 3D Vertical": {"translate": [{"time": 0, "x": 7.62, "y": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.5, "x": -100, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 115.25, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2, "x": 7.62, "y": 0}]}, "Body 3D Horizontal": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": -0.64, "y": -100, "curve": "stepped"}, {"time": 1.3333, "x": -0.64, "y": -100, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head 3D Horizontal": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": -15.95, "y": -166.67, "curve": "stepped"}, {"time": 1.3333, "x": -15.95, "y": -166.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 6, "curve": "stepped"}, {"time": 1.3333, "angle": 6, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Body Bend Over": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 9.05, "y": -0.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}}, "deform": {"default": {"Head": {"Head": [{"time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "offset": 128, "vertices": [-1.44913, 0, -1.44913, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0, 0, 0, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.44913, 0, -1.44913, 0, 0, 0, 0, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -1.82721, 0, -1.82721, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.82721, 0, -1.82721], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "curve": "stepped"}, {"time": 1.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "offset": 128, "vertices": [-1.44913, 0, -1.44913, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0, 0, 0, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.44913, 0, -1.44913, 0, 0, 0, 0, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -1.82721, 0, -1.82721, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.82721, 0, -1.82721], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333}]}}}}, "IdleTiltRight": {"bones": {"Hand Left": {"rotate": [{"time": 0, "angle": -22}]}, "Hand Right": {"rotate": [{"time": 0, "angle": 22}]}, "Forearm Right IK": {"translate": [{"time": 0, "x": 32.5, "y": 18}]}, "Forearm Left IK": {"translate": [{"time": 0, "x": -32.5, "y": 18}]}, "Finger Left 1": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Left 2": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Left 3": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Right 1": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Right 2": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Right 3": {"rotate": [{"time": 0, "angle": -12}]}, "Chest": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1.12, "y": 1.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "Breast": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 3.91, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Body 3D Vertical": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 25, "y": -0.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head 3D Vertical": {"translate": [{"time": 0, "x": 7.62, "y": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.5, "x": -100, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 115.25, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2, "x": 7.62, "y": 0}]}, "Body 3D Horizontal": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0.64, "y": 100, "curve": "stepped"}, {"time": 1.3333, "x": 0.64, "y": 100, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head 3D Horizontal": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": -15.95, "y": 166.67, "curve": "stepped"}, {"time": 1.3333, "x": -15.95, "y": 166.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -6, "curve": "stepped"}, {"time": 1.3333, "angle": -6, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Body Bend Over": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 9.05, "y": -0.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}}, "deform": {"default": {"Head": {"Head": [{"time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "offset": 128, "vertices": [-1.44913, 0, -1.44913, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0, 0, 0, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.44913, 0, -1.44913, 0, 0, 0, 0, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -1.82721, 0, -1.82721, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.82721, 0, -1.82721], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "curve": "stepped"}, {"time": 1.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "offset": 128, "vertices": [-1.44913, 0, -1.44913, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0, 0, 0, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.44913, 0, -1.44913, 0, 0, 0, 0, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -1.82721, 0, -1.82721, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.82721, 0, -1.82721], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333}]}}}}, "InviteBet": {"slots": {"Bubble Invite Bet": {"attachment": [{"time": 0, "name": "Bubble Invite Bet"}]}, "Forearm 6": {"attachment": [{"time": 0.3333, "name": "Forearm 6"}, {"time": 1.6333, "name": null}]}, "Forearm Left": {"attachment": [{"time": 0.3333, "name": null}, {"time": 1.6333, "name": "Forearm 1"}]}, "Forearm Left Shadow": {"color": [{"time": 0, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffffff"}]}}, "bones": {"Hand Left": {"rotate": [{"time": 0, "angle": -22}, {"time": 0.5, "angle": -0.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -10, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -0.14, "curve": "stepped"}, {"time": 1.3333, "angle": -0.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -22}]}, "Hand Right": {"rotate": [{"time": 0, "angle": 22}, {"time": 0.3333, "angle": 22.05, "curve": "stepped"}, {"time": 1.5, "angle": 22.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 22}]}, "Forearm Right IK": {"translate": [{"time": 0, "x": 32.5, "y": 18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 29.01, "y": 6.42, "curve": "stepped"}, {"time": 1.5, "x": 29.01, "y": 6.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 32.5, "y": 18}]}, "Forearm Left IK": {"translate": [{"time": 0, "x": -32.5, "y": 18}]}, "Finger Left 1": {"rotate": [{"time": 0, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 0, "curve": "stepped"}, {"time": 1.6, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Finger Left 2": {"rotate": [{"time": 0, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 0, "curve": "stepped"}, {"time": 1.6, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Finger Left 3": {"rotate": [{"time": 0, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 0, "curve": "stepped"}, {"time": 1.6, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Finger Right 1": {"rotate": [{"time": 0, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 5, "curve": "stepped"}, {"time": 1.5, "angle": 5, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Finger Right 2": {"rotate": [{"time": 0, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 5, "curve": "stepped"}, {"time": 1.5, "angle": 5, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Finger Right 3": {"rotate": [{"time": 0, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 5, "curve": "stepped"}, {"time": 1.5, "angle": 5, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Chest": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1.12, "y": 1.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "Breast": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 3.91, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Body 3D Vertical": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 25, "y": -0.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head 3D Vertical": {"translate": [{"time": 0, "x": 7.62, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": -189.79, "y": -33.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 203.59, "y": -123.62, "curve": "stepped"}, {"time": 1.5, "x": 203.59, "y": -123.62, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 7.62, "y": 0}]}, "Forearm Left": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": -179.39, "curve": [0.222, 0.3, 0.75, 1]}, {"time": 0.6667, "angle": 160.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 167.3, "curve": "stepped"}, {"time": 1.3333, "angle": 167.3, "curve": [0.25, 0, 0.838, 0.8]}, {"time": 1.5, "angle": -179.39, "curve": [0.222, 0.3, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Arm Left": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": -3.57, "y": -7.66, "curve": "stepped"}, {"time": 1.5, "x": -3.57, "y": -7.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Body": {"rotate": [{"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 4.6, "curve": "stepped"}, {"time": 1.5, "angle": 4.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Head": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -9.64, "curve": "stepped"}, {"time": 1.5, "angle": -9.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Bubble Talk": {"scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0, "y": 0}]}}, "ik": {"Forearm Left IK": [{"time": 0, "mix": 0.999, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "mix": 0, "bendPositive": false, "curve": "stepped"}, {"time": 1.5, "mix": 0, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "mix": 0.999, "bendPositive": false}]}, "deform": {"default": {"Forearm Left": {"Forearm 1": [{"time": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "offset": 4, "vertices": [7.24745, 4.82777, -9.9455, 2.08982, 10.80641, 6.43013, -14.16231, 3.63447, -4.32572, -0.72043], "curve": "stepped"}, {"time": 1.6333, "offset": 4, "vertices": [7.24745, 4.82777, -9.9455, 2.08982, 10.80641, 6.43013, -14.16231, 3.63447, -4.32572, -0.72043], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333}]}, "Head": {"Head": [{"time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "offset": 220, "vertices": [-4.16299, -0.04781, -4.16299, -0.04778, -4.16299, -0.04781, -4.16299, -0.04778, -1.78018, 0.21401, -1.78018, 0.21404, 0, 0, 0, 0, 0.54822, -0.11459, 0.54822, -0.11459, -4.9823, -0.19227, -4.98224, -0.19224, -4.9823, -0.19227, -4.98224, -0.19224, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.78018, 0.21401, -1.78018, 0.21404], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5}]}}}}, "ResultBanker": {"slots": {"Forearm Left": {"attachment": [{"time": 0.3333, "name": "Forearm 5"}, {"time": 1.7333, "name": "Forearm 4"}, {"time": 2, "name": "Forearm 1"}]}, "Forearm Left Shadow": {"color": [{"time": 0, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffffff"}]}}, "bones": {"Hand Left": {"rotate": [{"time": 0, "angle": -22}, {"time": 0.2667, "angle": -31.94, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -22}]}, "Forearm Left IK": {"translate": [{"time": 0, "x": -32.5, "y": 18}]}, "Finger Left 1": {"rotate": [{"time": 0, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 0, "curve": "stepped"}, {"time": 1.7333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Finger Left 2": {"rotate": [{"time": 0, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 0, "curve": "stepped"}, {"time": 1.7333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Finger Left 3": {"rotate": [{"time": 0, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 0, "curve": "stepped"}, {"time": 1.7333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Chest": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1.12, "y": 1.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "Breast": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 3.91, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Body 3D Vertical": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 25, "y": -0.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head 3D Vertical": {"translate": [{"time": 0, "x": 7.62, "y": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.5, "x": -100, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 115.25, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2, "x": 7.62, "y": 0}]}, "Body Bend Over": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 9.05, "y": -0.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Forearm Right IK": {"translate": [{"time": 0, "x": 32.5, "y": 18}]}, "Body 3D Horizontal": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0.46, "y": -73.53, "curve": "stepped"}, {"time": 1.5, "x": 0.46, "y": -73.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head 3D Horizontal": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": -97.96, "y": -287.29, "curve": "stepped"}, {"time": 1.5, "x": -97.96, "y": -287.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 5.36, "curve": "stepped"}, {"time": 1.5, "angle": 5.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Forearm Left": {"rotate": [{"time": 0, "angle": -45.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 51.44}]}, "Clavicle Right": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 10, "curve": "stepped"}, {"time": 1.5, "angle": 10, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Hand Right": {"rotate": [{"time": 0, "angle": 22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 12.51, "curve": "stepped"}, {"time": 1.5, "angle": 12.51, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 22}]}, "Clavicle Left": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -5, "curve": "stepped"}, {"time": 1.6667, "angle": -5, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Finger Right 3": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Right 2": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Right 1": {"rotate": [{"time": 0, "angle": -12}]}}, "ik": {"Forearm Left IK": [{"time": 0, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "mix": 0, "bendPositive": false, "curve": "stepped"}, {"time": 1.6667, "mix": 0, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "bendPositive": false}]}, "deform": {"default": {"Arm Left": {"Arm": [{"time": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "offset": 28, "vertices": [6.04708, 0.80285, 6.04708, 0.80285, 6.04708, 0.80285, 6.04708, 0.80285, 6.04713, 1.34978, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.047, 2.20141], "curve": "stepped"}, {"time": 1.6667, "offset": 28, "vertices": [6.04708, 0.80285, 6.04708, 0.80285, 6.04708, 0.80285, 6.04708, 0.80285, 6.04713, 1.34978, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.047, 2.20141], "curve": [0.25, 0, 0.75, 1]}, {"time": 2}]}}}}, "ResultPlayer": {"slots": {"Forearm Right": {"attachment": [{"time": 0.3333, "name": "Forearm 5"}, {"time": 1.7333, "name": "Forearm 4"}, {"time": 2, "name": "Forearm 1"}]}, "Forearm Right Shadow": {"color": [{"time": 0, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffffff"}]}}, "bones": {"Hand Left": {"rotate": [{"time": 0, "angle": -22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -11.29, "curve": "stepped"}, {"time": 1.5, "angle": -11.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -18}]}, "Hand Right": {"rotate": [{"time": 0, "angle": 22}, {"time": 0.2667, "angle": 36.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 18}]}, "Forearm Left IK": {"translate": [{"time": 0, "x": -32.5, "y": 18}]}, "Finger Left 1": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Left 2": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Left 3": {"rotate": [{"time": 0, "angle": -12}]}, "Chest": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1.12, "y": 1.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "Breast": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 3.91, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Body 3D Vertical": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 25, "y": -0.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head 3D Vertical": {"translate": [{"time": 0, "x": 7.62, "y": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.5, "x": -100, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 115.25, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2, "x": 7.62, "y": 0}]}, "Body Bend Over": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 9.05, "y": -0.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Forearm Right": {"rotate": [{"time": 0, "angle": 20.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -42.16}]}, "Forearm Right IK": {"translate": [{"time": 0, "x": 32.5, "y": 18}]}, "Body 3D Horizontal": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0.46, "y": 72.54, "curve": "stepped"}, {"time": 1.5, "x": 0.46, "y": 72.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head 3D Horizontal": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": -143.64, "y": 199.2, "curve": "stepped"}, {"time": 1.5, "x": -143.64, "y": 199.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -5, "curve": "stepped"}, {"time": 1.5, "angle": -5, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Clavicle Left": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -10, "curve": "stepped"}, {"time": 1.5, "angle": -10, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Finger Right 1": {"rotate": [{"time": 0, "angle": -12}, {"time": 0.2667, "angle": 0, "curve": "stepped"}, {"time": 1.7333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 7.5}]}, "Finger Right 2": {"rotate": [{"time": 0, "angle": -12}, {"time": 0.2667, "angle": 0, "curve": "stepped"}, {"time": 1.7333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 7.5}]}, "Finger Right 3": {"rotate": [{"time": 0, "angle": -12}, {"time": 0.2667, "angle": 0, "curve": "stepped"}, {"time": 1.7333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 7.5}]}, "Clavicle Right": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 5, "curve": "stepped"}, {"time": 1.6667, "angle": 5, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}}, "ik": {"Forearm Right IK": [{"time": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "mix": 0, "curve": "stepped"}, {"time": 1.6667, "mix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2}]}, "deform": {"default": {"Arm Right": {"Arm": [{"time": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "offset": 28, "vertices": [5.54707, 0, 5.54707, 0, 5.54707, 0, 5.54707, 0, 5.5471, -1.24834, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.54711, -1.32107], "curve": "stepped"}, {"time": 1.6667, "offset": 28, "vertices": [5.54707, 0, 5.54707, 0, 5.54707, 0, 5.54707, 0, 5.5471, -1.24834, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.54711, -1.32107], "curve": [0.25, 0, 0.75, 1]}, {"time": 2}]}}}}, "ResultTie": {"slots": {"Forearm Left": {"attachment": [{"time": 0.3333, "name": "Forearm 5"}, {"time": 1.7333, "name": "Forearm 4"}, {"time": 2, "name": "Forearm 1"}]}, "Forearm Left Shadow": {"color": [{"time": 0, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffffff"}]}, "Forearm Right": {"attachment": [{"time": 0.3333, "name": "Forearm 5"}, {"time": 1.7333, "name": "Forearm 4"}, {"time": 2, "name": "Forearm 1"}]}, "Forearm Right Shadow": {"color": [{"time": 0, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffffff"}]}}, "bones": {"Hand Left": {"rotate": [{"time": 0, "angle": -22}, {"time": 0.2667, "angle": -31.94, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -22}]}, "Hand Right": {"rotate": [{"time": 0, "angle": 22}, {"time": 0.2667, "angle": 36.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 22}]}, "Forearm Left IK": {"translate": [{"time": 0, "x": -32.5, "y": 18}]}, "Finger Left 1": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Left 2": {"rotate": [{"time": 0, "angle": -12}]}, "Finger Left 3": {"rotate": [{"time": 0, "angle": -12}]}, "Chest": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1.12, "y": 1.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "Breast": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 3.91, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Body 3D Vertical": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 25, "y": -0.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head 3D Vertical": {"translate": [{"time": 0, "x": 7.62, "y": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.5, "x": -100, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 115.25, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2, "x": 7.62, "y": 0}]}, "Body Bend Over": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 9.05, "y": -0.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Forearm Right": {"rotate": [{"time": 0, "angle": 20.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -42.16}]}, "Forearm Right IK": {"translate": [{"time": 0, "x": 32.5, "y": 18}]}, "Head 3D Horizontal": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": -142.37, "y": -0.8, "curve": "stepped"}, {"time": 1.5, "x": -142.37, "y": -0.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Clavicle Left": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -5, "curve": "stepped"}, {"time": 1.6667, "angle": -5, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Finger Right 1": {"rotate": [{"time": 0, "angle": -12}, {"time": 0.2667, "angle": 0, "curve": "stepped"}, {"time": 1.7333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Finger Right 2": {"rotate": [{"time": 0, "angle": -12}, {"time": 0.2667, "angle": 0, "curve": "stepped"}, {"time": 1.7333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Finger Right 3": {"rotate": [{"time": 0, "angle": -12}, {"time": 0.2667, "angle": 0, "curve": "stepped"}, {"time": 1.7333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Clavicle Right": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 5, "curve": "stepped"}, {"time": 1.6667, "angle": 5, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Forearm Left": {"rotate": [{"time": 0, "angle": -45.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 51.44}]}}, "ik": {"Forearm Left IK": [{"time": 0, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "mix": 0, "bendPositive": false, "curve": "stepped"}, {"time": 1.6667, "mix": 0, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "bendPositive": false}], "Forearm Right IK": [{"time": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "mix": 0, "curve": "stepped"}, {"time": 1.6667, "mix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2}]}, "deform": {"default": {"Arm Left": {"Arm": [{"time": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "offset": 28, "vertices": [6.04708, 0.80285, 6.04708, 0.80285, 6.04708, 0.80285, 6.04708, 0.80285, 6.04713, 1.34978, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.047, 2.20141], "curve": "stepped"}, {"time": 1.6667, "offset": 28, "vertices": [6.04708, 0.80285, 6.04708, 0.80285, 6.04708, 0.80285, 6.04708, 0.80285, 6.04713, 1.34978, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.047, 2.20141], "curve": [0.25, 0, 0.75, 1]}, {"time": 2}]}, "Arm Right": {"Arm": [{"time": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "offset": 28, "vertices": [5.54707, 0, 5.54707, 0, 5.54707, 0, 5.54707, 0, 5.5471, -1.24834, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.54711, -1.32107], "curve": "stepped"}, {"time": 1.6667, "offset": 28, "vertices": [5.54707, 0, 5.54707, 0, 5.54707, 0, 5.54707, 0, 5.5471, -1.24834, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.54711, -1.32107], "curve": [0.25, 0, 0.75, 1]}, {"time": 2}]}}}}, "StopBet": {"slots": {"Bubble Stop Bet": {"attachment": [{"time": 0, "name": "Bubble Stop Bet"}]}, "Forearm 6": {"attachment": [{"time": 0.3333, "name": "Forearm 6"}, {"time": 1.6333, "name": null}]}, "Forearm Left": {"attachment": [{"time": 0.3333, "name": null}, {"time": 1.6333, "name": "Forearm 1"}]}, "Forearm Left Shadow": {"color": [{"time": 0, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffffff"}]}}, "bones": {"Hand Left": {"rotate": [{"time": 0, "angle": -22}, {"time": 0.5, "angle": -0.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -10, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -0.14, "curve": "stepped"}, {"time": 1.3333, "angle": -0.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -22}]}, "Hand Right": {"rotate": [{"time": 0, "angle": 22}, {"time": 0.3333, "angle": 22.05, "curve": "stepped"}, {"time": 1.5, "angle": 22.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 22}]}, "Forearm Right IK": {"translate": [{"time": 0, "x": 32.5, "y": 18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 29.01, "y": 6.42, "curve": "stepped"}, {"time": 1.5, "x": 29.01, "y": 6.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 32.5, "y": 18}]}, "Forearm Left IK": {"translate": [{"time": 0, "x": -32.5, "y": 18}]}, "Finger Left 1": {"rotate": [{"time": 0, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 0, "curve": "stepped"}, {"time": 1.6, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Finger Left 2": {"rotate": [{"time": 0, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 0, "curve": "stepped"}, {"time": 1.6, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Finger Left 3": {"rotate": [{"time": 0, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 0, "curve": "stepped"}, {"time": 1.6, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Finger Right 1": {"rotate": [{"time": 0, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 5, "curve": "stepped"}, {"time": 1.5, "angle": 5, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Finger Right 2": {"rotate": [{"time": 0, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 5, "curve": "stepped"}, {"time": 1.5, "angle": 5, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Finger Right 3": {"rotate": [{"time": 0, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 5, "curve": "stepped"}, {"time": 1.5, "angle": 5, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Chest": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1.12, "y": 1.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "Breast": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 3.91, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Body 3D Vertical": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 25, "y": -0.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head 3D Vertical": {"translate": [{"time": 0, "x": 7.62, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": -189.79, "y": -33.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 203.59, "y": -123.62, "curve": "stepped"}, {"time": 1.5, "x": 203.59, "y": -123.62, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 7.62, "y": 0}]}, "Forearm Left": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": -179.39, "curve": [0.222, 0.3, 0.75, 1]}, {"time": 0.6667, "angle": 160.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 167.3, "curve": "stepped"}, {"time": 1.3333, "angle": 167.3, "curve": [0.25, 0, 0.838, 0.8]}, {"time": 1.5, "angle": -179.39, "curve": [0.222, 0.3, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Arm Left": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": -3.57, "y": -7.66, "curve": "stepped"}, {"time": 1.5, "x": -3.57, "y": -7.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Body": {"rotate": [{"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 4.6, "curve": "stepped"}, {"time": 1.5, "angle": 4.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Head": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -9.64, "curve": "stepped"}, {"time": 1.5, "angle": -9.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Bubble Talk": {"scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0, "y": 0}]}}, "ik": {"Forearm Left IK": [{"time": 0, "mix": 0.999, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "mix": 0, "bendPositive": false, "curve": "stepped"}, {"time": 1.5, "mix": 0, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "mix": 0.999, "bendPositive": false}]}, "deform": {"default": {"Forearm Left": {"Forearm 1": [{"time": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "offset": 4, "vertices": [7.24745, 4.82777, -9.9455, 2.08982, 10.80641, 6.43013, -14.16231, 3.63447, -4.32572, -0.72043], "curve": "stepped"}, {"time": 1.6333, "offset": 4, "vertices": [7.24745, 4.82777, -9.9455, 2.08982, 10.80641, 6.43013, -14.16231, 3.63447, -4.32572, -0.72043], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333}]}, "Head": {"Head": [{"time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "offset": 220, "vertices": [-4.16299, -0.04781, -4.16299, -0.04778, -4.16299, -0.04781, -4.16299, -0.04778, -1.78018, 0.21401, -1.78018, 0.21404, 0, 0, 0, 0, 0.54822, -0.11459, 0.54822, -0.11459, -4.9823, -0.19227, -4.98224, -0.19224, -4.9823, -0.19227, -4.98224, -0.19224, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.78018, 0.21401, -1.78018, 0.21404], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5}]}}}}, "TakeCard1": {"slots": {"Card 1": {"attachment": [{"time": 0.8333, "name": "Card"}, {"time": 1.5667, "name": null}]}, "Forearm Left": {"attachment": [{"time": 0.6667, "name": "Forearm 3"}, {"time": 0.8333, "name": "Forearm 1"}]}, "Forearm Left Shadow": {"color": [{"time": 0.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "color": "ffffffff"}]}, "Forearm Right": {"attachment": [{"time": 1.5, "name": "Forearm 2"}, {"time": 1.7, "name": "Forearm 1"}]}, "Forearm Right Shadow": {"color": [{"time": 1.2333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffffff"}]}}, "bones": {"Hand Left": {"rotate": [{"time": 0, "angle": -22, "curve": "stepped"}, {"time": 0.3333, "angle": -22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 13.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -22}]}, "Forearm Left IK": {"translate": [{"time": 0, "x": -32.5, "y": 18}]}, "Finger Left 1": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.3333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -12}]}, "Finger Left 2": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.3333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -12}]}, "Finger Left 3": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.3333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -12}]}, "Finger Right 2": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 1, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Finger Right 3": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 1, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Forearm Left": {"rotate": [{"time": 0, "angle": -44.89, "curve": "stepped"}, {"time": 0.3333, "angle": -44.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 34.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -13.52}], "scale": [{"time": 0.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 0.882, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 1, "y": 1}]}, "Body 3D Horizontal": {"translate": [{"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -3.38, "y": -144.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -0.63, "y": 90.54, "curve": "stepped"}, {"time": 1.5667, "x": -0.63, "y": 90.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Body": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -4.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 3.5, "curve": "stepped"}, {"time": 1.5667, "angle": 3.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Clavicle Left": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 14.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0}]}, "Head 3D Horizontal": {"translate": [{"time": 0.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -173.4, "y": -13.88, "curve": "stepped"}, {"time": 1, "x": -173.4, "y": -13.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0, "y": 197.8, "curve": "stepped"}, {"time": 1.5667, "x": 0, "y": 197.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Forearm Right IK": {"translate": [{"time": 0, "x": 32.5, "y": 18, "curve": "stepped"}, {"time": 0.3333, "x": 32.5, "y": 18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 47.26, "y": 8.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 41.07, "y": 7.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -36.43, "y": 10.48, "curve": "stepped"}, {"time": 1.5667, "x": -36.43, "y": 10.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 32.5, "y": 18}]}, "Hand Right": {"rotate": [{"time": 0, "angle": 22, "curve": "stepped"}, {"time": 0.3333, "angle": 22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 15.09, "curve": "stepped"}, {"time": 1, "angle": 15.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -3.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 22}]}, "Hip Bend Over": {"translate": [{"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 10, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -10, "y": 0, "curve": "stepped"}, {"time": 1.5667, "x": -10, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 12.53, "curve": "stepped"}, {"time": 1, "angle": 12.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -6, "curve": "stepped"}, {"time": 1.5667, "angle": -6, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Body Bend Over": {"translate": [{"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -29.6, "y": -2.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -44.24, "y": -0.46, "curve": "stepped"}, {"time": 1.5667, "x": -44.24, "y": -0.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Forearm Right": {"scale": [{"time": 1.2333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0.824, "y": 0.926, "curve": "stepped"}, {"time": 1.5667, "x": 0.824, "y": 0.926, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "Finger Right 1": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 1, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}], "scale": [{"time": 1.4, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0.15, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 0.15, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "x": 1, "y": 1}]}, "Chest": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1.12, "y": 1.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "Breast": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 3.91, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Body 3D Vertical": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 25, "y": -0.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head 3D Vertical": {"translate": [{"time": 0, "x": 7.62, "y": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.5, "x": -100, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 115.25, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2, "x": 7.62, "y": 0}]}, "Clavicle Right": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 16.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -15, "curve": "stepped"}, {"time": 1.5667, "angle": -15, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Arm Left": {"rotate": [{"time": 0, "angle": 0.13, "curve": "stepped"}, {"time": 0.3333, "angle": 0.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 8.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0.13}]}, "Card": {"scale": [{"time": 1.4333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "x": 1, "y": 0}]}, "Hand Right Scale": {"scale": [{"time": 1.2333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1.3, "y": 0.926, "curve": "stepped"}, {"time": 1.5667, "x": 1.3, "y": 0.926, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}}, "ik": {"Forearm Left IK": [{"time": 0.3333, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "mix": 0, "bendPositive": false, "curve": "stepped"}, {"time": 1, "mix": 0, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "bendPositive": false}]}, "transform": {"Card Hand Left": [{"time": 0, "rotateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": "stepped"}, {"time": 0.8333, "rotateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "rotateMix": 0, "scaleMix": 0, "shearMix": 0}], "Card hand Right": [{"time": 1, "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "rotateMix": 0, "scaleMix": 0, "shearMix": 0}]}, "deform": {"default": {"Arm Left": {"Arm": [{"time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "offset": 28, "vertices": [1.62057, -0.43823, 1.75935, 0, 1.75935, 0, 1.75935, 0, 2.9482, 0.5834, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.75935], "curve": [0.25, 0, 0.75, 1]}, {"time": 2}]}, "Forearm Right Shadow": {"Forearm Shadow": [{"time": 0, "curve": "stepped"}, {"time": 1.5, "offset": 26, "vertices": [-3.61166, -3.36191, -3.65649, -3.313, -7.99842, -3.12586, -8.04015, -3.01786, -12.12449, -1.94302, -12.14941, -1.77921, -16.90559, 0.92504, -16.90559, 0.92504, -12.12449, -1.94302, -12.14941, -1.77921, -7.99842, -3.12586, -8.04015, -3.01786, -3.61166, -3.36191, -3.65649, -3.313], "curve": "stepped"}, {"time": 1.7}]}, "Head": {"Head": [{"time": 1.5667, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "offset": 128, "vertices": [-1.44913, 0, -1.44913, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0, 0, 0, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.44913, 0, -1.44913, 0, 0, 0, 0, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -1.82721, 0, -1.82721, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.82721, 0, -1.82721], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9}]}}}}, "TakeCard2": {"slots": {"Card 1": {"attachment": [{"time": 0.8333, "name": "Card"}, {"time": 1.5667, "name": null}]}, "Forearm Left": {"attachment": [{"time": 0.6667, "name": "Forearm 3"}, {"time": 0.8333, "name": "Forearm 1"}]}, "Forearm Left Shadow": {"color": [{"time": 0.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "color": "ffffffff"}]}, "Forearm Right": {"attachment": [{"time": 1.5, "name": "Forearm 2"}, {"time": 1.7, "name": "Forearm 1"}]}}, "bones": {"Hand Left": {"rotate": [{"time": 0, "angle": -22, "curve": "stepped"}, {"time": 0.3333, "angle": -22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 13.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -22}]}, "Forearm Left IK": {"translate": [{"time": 0, "x": -32.5, "y": 18}]}, "Finger Left 1": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.3333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -12}]}, "Finger Left 2": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.3333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -12}]}, "Finger Left 3": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.3333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -12}]}, "Finger Right 2": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 1, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Finger Right 3": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 1, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Forearm Left": {"rotate": [{"time": 0, "angle": -44.89, "curve": "stepped"}, {"time": 0.3333, "angle": -44.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 34.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -13.52}], "scale": [{"time": 0.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 0.882, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 1, "y": 1}]}, "Body 3D Horizontal": {"translate": [{"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -3.38, "y": -144.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -0.63, "y": -148.54, "curve": "stepped"}, {"time": 1.5667, "x": -0.63, "y": -148.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Body": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -4.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -2.81, "curve": "stepped"}, {"time": 1.5667, "angle": -2.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Clavicle Left": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 14.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0}]}, "Head 3D Horizontal": {"translate": [{"time": 0.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -173.4, "y": -13.88, "curve": "stepped"}, {"time": 1, "x": -173.4, "y": -13.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 2.71, "y": -227.53, "curve": "stepped"}, {"time": 1.5667, "x": 2.71, "y": -227.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Forearm Right IK": {"translate": [{"time": 0, "x": 32.5, "y": 18, "curve": "stepped"}, {"time": 0.3333, "x": 32.5, "y": 18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 47.26, "y": 8.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 41.07, "y": 7.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 35.4, "y": -4.11, "curve": "stepped"}, {"time": 1.5667, "x": 35.4, "y": -4.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 32.5, "y": 18}]}, "Hand Right": {"rotate": [{"time": 0, "angle": 22, "curve": "stepped"}, {"time": 0.3333, "angle": 22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 15.09, "curve": "stepped"}, {"time": 1, "angle": 15.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -3.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 22}]}, "Hip Bend Over": {"translate": [{"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 10, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -10, "y": 0, "curve": "stepped"}, {"time": 1.5667, "x": -10, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 12.53, "curve": "stepped"}, {"time": 1, "angle": 12.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 6, "curve": "stepped"}, {"time": 1.5667, "angle": 6, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Body Bend Over": {"translate": [{"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -29.6, "y": -2.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -47, "y": -0.44, "curve": "stepped"}, {"time": 1.5667, "x": -47, "y": -0.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Forearm Right": {"scale": [{"time": 1.2333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0.824, "y": 0.926, "curve": "stepped"}, {"time": 1.5667, "x": 0.824, "y": 0.926, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "Finger Right 1": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 1, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}], "scale": [{"time": 1.4, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0.15, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 0.15, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "x": 1, "y": 1}]}, "Chest": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1.12, "y": 1.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "Breast": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 3.91, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Body 3D Vertical": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 25, "y": -0.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head 3D Vertical": {"translate": [{"time": 0, "x": 7.62, "y": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.5, "x": -100, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 115.25, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2, "x": 7.62, "y": 0}]}, "Clavicle Right": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 16.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 20, "curve": "stepped"}, {"time": 1.5667, "angle": 20, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Arm Left": {"rotate": [{"time": 0, "angle": 0.13, "curve": "stepped"}, {"time": 0.3333, "angle": 0.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 8.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0.13}]}, "Hip": {"translate": [{"time": 1.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 5, "y": 0, "curve": "stepped"}, {"time": 1.5667, "x": 5, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Card": {"scale": [{"time": 1.4333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "x": 1, "y": 0}]}, "Hand Right Scale": {"scale": [{"time": 1.2333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1.3, "y": 0.926, "curve": "stepped"}, {"time": 1.5667, "x": 1.3, "y": 0.926, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}}, "ik": {"Forearm Left IK": [{"time": 0.3333, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "mix": 0, "bendPositive": false, "curve": "stepped"}, {"time": 1, "mix": 0, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "bendPositive": false}]}, "transform": {"Card Hand Left": [{"time": 0, "rotateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": "stepped"}, {"time": 0.8333, "rotateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "rotateMix": 0, "scaleMix": 0, "shearMix": 0}], "Card hand Right": [{"time": 1, "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "rotateMix": 0, "scaleMix": 0, "shearMix": 0}]}, "deform": {"default": {"Arm Left": {"Arm": [{"time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "offset": 28, "vertices": [1.62057, -0.43823, 1.75935, 0, 1.75935, 0, 1.75935, 0, 2.9482, 0.5834, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.75935], "curve": [0.25, 0, 0.75, 1]}, {"time": 2}]}, "Forearm Right Shadow": {"Forearm Shadow": [{"time": 0, "curve": "stepped"}, {"time": 1.5, "offset": 26, "vertices": [-3.61166, -3.36191, -3.65649, -3.313, -7.99842, -3.12586, -8.04015, -3.01786, -12.12449, -1.94302, -12.14941, -1.77921, -16.90559, 0.92504, -16.90559, 0.92504, -12.12449, -1.94302, -12.14941, -1.77921, -7.99842, -3.12586, -8.04015, -3.01786, -3.61166, -3.36191, -3.65649, -3.313], "curve": "stepped"}, {"time": 1.7}]}, "Head": {"Head": [{"time": 1.5667, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "offset": 128, "vertices": [-1.44913, 0, -1.44913, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0, 0, 0, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.44913, 0, -1.44913, 0, 0, 0, 0, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -1.82721, 0, -1.82721, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.82721, 0, -1.82721], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9}]}}}}, "TakeCard3": {"slots": {"Card 1": {"attachment": [{"time": 0.6667, "name": "Card"}, {"time": 1.4, "name": null}]}, "Forearm Left": {"attachment": [{"time": 0.5, "name": "Forearm 3"}, {"time": 0.6667, "name": "Forearm 1"}]}, "Forearm Left Shadow": {"color": [{"time": 0.1667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "color": "ffffffff"}]}, "Forearm Right Shadow": {"color": [{"time": 1.0667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "color": "ffffffff"}]}}, "bones": {"Hand Left": {"rotate": [{"time": 0, "angle": -22, "curve": "stepped"}, {"time": 0.1667, "angle": -22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 13.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -22}]}, "Forearm Left IK": {"translate": [{"time": 0, "x": -32.5, "y": 18}]}, "Finger Left 1": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.1667, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -12}]}, "Finger Left 2": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.1667, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -12}]}, "Finger Left 3": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.1667, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -12}]}, "Finger Right 2": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.8333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": -12}]}, "Finger Right 3": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.8333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": -12}]}, "Forearm Left": {"rotate": [{"time": 0, "angle": -44.89, "curve": "stepped"}, {"time": 0.1667, "angle": -44.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 34.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -13.52}], "scale": [{"time": 0.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0.882, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "x": 1, "y": 1}]}, "Body 3D Horizontal": {"translate": [{"time": 0.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": -3.38, "y": -144.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "x": 0, "y": 0}]}, "Body": {"rotate": [{"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -4.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 0}]}, "Clavicle Left": {"rotate": [{"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 14.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 0}]}, "Head 3D Horizontal": {"translate": [{"time": 0.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": -173.4, "y": -13.88, "curve": "stepped"}, {"time": 0.8333, "x": -173.4, "y": -13.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 69.76, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 69.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Forearm Right IK": {"translate": [{"time": 0, "x": 32.5, "y": 18, "curve": "stepped"}, {"time": 0.1667, "x": 32.5, "y": 18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 47.26, "y": 8.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "x": 22.5, "y": 10.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 7.07, "y": 0.07, "curve": "stepped"}, {"time": 1.4, "x": 7.07, "y": 0.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 32.5, "y": 18}]}, "Hand Right": {"rotate": [{"time": 0, "angle": 22, "curve": "stepped"}, {"time": 0.1667, "angle": 22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 15.09, "curve": "stepped"}, {"time": 0.8333, "angle": 15.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -3.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 22}]}, "Hip Bend Over": {"translate": [{"time": 0.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 10, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "x": 0, "y": 0}]}, "Head": {"rotate": [{"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 12.53, "curve": "stepped"}, {"time": 0.8333, "angle": 12.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 0}]}, "Body Bend Over": {"translate": [{"time": 0.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": -29.6, "y": -2.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": -50, "y": 0.32, "curve": "stepped"}, {"time": 1.4, "x": -50, "y": 0.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Forearm Right": {"scale": [{"time": 1.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0.765, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 0.765, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 1, "y": 1}]}, "Finger Right 1": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.8333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": -12}]}, "Chest": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1.12, "y": 1.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 1, "y": 1}]}, "Breast": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 3.91, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Body 3D Vertical": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 25, "y": -0.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Head 3D Vertical": {"translate": [{"time": 0, "x": 7.62, "y": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.3333, "x": -100, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 115.25, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1.8333, "x": 7.62, "y": 0}]}, "Arm Left": {"rotate": [{"time": 0, "angle": 0.13, "curve": "stepped"}, {"time": 0.1667, "angle": 0.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 8.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 0.13}]}, "Clavicle Right": {"rotate": [{"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 16.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 10, "curve": "stepped"}, {"time": 1.4, "angle": 10, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Hand Right Scale": {"scale": [{"time": 1.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1.35, "y": 0.926, "curve": "stepped"}, {"time": 1.4, "x": 1.35, "y": 0.926, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 1, "y": 1}]}}, "ik": {"Forearm Left IK": [{"time": 0.1667, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "mix": 0, "bendPositive": false, "curve": "stepped"}, {"time": 0.8333, "mix": 0, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "bendPositive": false}]}, "transform": {"Card Hand Left": [{"time": 0, "rotateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": "stepped"}, {"time": 0.6667, "rotateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "rotateMix": 0, "scaleMix": 0, "shearMix": 0}], "Card hand Right": [{"time": 0.8333, "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "rotateMix": 0, "scaleMix": 0, "shearMix": 0}]}, "deform": {"default": {"Arm Left": {"Arm": [{"time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "offset": 28, "vertices": [1.62057, -0.43823, 1.75935, 0, 1.75935, 0, 1.75935, 0, 2.9482, 0.5834, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.75935], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333}]}, "Head": {"Head": [{"time": 1.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "offset": 128, "vertices": [-1.44913, 0, -1.44913, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0, 0, 0, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.44913, 0, -1.44913, 0, 0, 0, 0, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -1.82721, 0, -1.82721, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.82721, 0, -1.82721], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333}]}}}}, "TakeCard4": {"slots": {"Card 1": {"attachment": [{"time": 0.6667, "name": "Card"}, {"time": 1.4, "name": null}]}, "Forearm Left": {"attachment": [{"time": 0.5, "name": "Forearm 3"}, {"time": 0.6667, "name": "Forearm 1"}]}, "Forearm Left Shadow": {"color": [{"time": 0.1667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "color": "ffffffff"}]}}, "bones": {"Hand Left": {"rotate": [{"time": 0, "angle": -22, "curve": "stepped"}, {"time": 0.1667, "angle": -22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 13.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -22}]}, "Forearm Left IK": {"translate": [{"time": 0, "x": -32.5, "y": 18}]}, "Finger Left 1": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.1667, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -12}]}, "Finger Left 2": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.1667, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -12}]}, "Finger Left 3": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.1667, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -12}]}, "Finger Right 2": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.8333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": -12}]}, "Finger Right 3": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.8333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": -12}]}, "Forearm Left": {"rotate": [{"time": 0, "angle": -44.89, "curve": "stepped"}, {"time": 0.1667, "angle": -44.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 34.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -13.52}], "scale": [{"time": 0.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0.882, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "x": 1, "y": 1}]}, "Body 3D Horizontal": {"translate": [{"time": 0.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": -3.38, "y": -144.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": -0.63, "y": -148.54, "curve": "stepped"}, {"time": 1.4, "x": -0.63, "y": -148.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Body": {"rotate": [{"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -4.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -2.81, "curve": "stepped"}, {"time": 1.4, "angle": -2.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Clavicle Left": {"rotate": [{"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 14.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 0}]}, "Head 3D Horizontal": {"translate": [{"time": 0.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": -173.4, "y": -13.88, "curve": "stepped"}, {"time": 0.8333, "x": -173.4, "y": -13.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 2.71, "y": -227.53, "curve": "stepped"}, {"time": 1.4, "x": 2.71, "y": -227.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Forearm Right IK": {"translate": [{"time": 0, "x": 32.5, "y": 18, "curve": "stepped"}, {"time": 0.1667, "x": 32.5, "y": 18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 47.26, "y": 8.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "x": 41.07, "y": 7.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 35.4, "y": -4.11, "curve": "stepped"}, {"time": 1.4, "x": 35.4, "y": -4.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 32.5, "y": 18}]}, "Hand Right": {"rotate": [{"time": 0, "angle": 22, "curve": "stepped"}, {"time": 0.1667, "angle": 22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 15.09, "curve": "stepped"}, {"time": 0.8333, "angle": 15.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -3.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 22}]}, "Hip Bend Over": {"translate": [{"time": 0.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 10, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": -10, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": -10, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Head": {"rotate": [{"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 12.53, "curve": "stepped"}, {"time": 0.8333, "angle": 12.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 6, "curve": "stepped"}, {"time": 1.4, "angle": 6, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Body Bend Over": {"translate": [{"time": 0.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": -29.6, "y": -2.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": -47, "y": -0.44, "curve": "stepped"}, {"time": 1.4, "x": -47, "y": -0.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Forearm Right": {"scale": [{"time": 1.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0.824, "y": 0.926, "curve": "stepped"}, {"time": 1.4, "x": 0.824, "y": 0.926, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 1, "y": 1}]}, "Finger Right 1": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.8333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": -12}]}, "Chest": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1.12, "y": 1.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 1, "y": 1}]}, "Breast": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 3.91, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Body 3D Vertical": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 25, "y": -0.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Head 3D Vertical": {"translate": [{"time": 0, "x": 7.62, "y": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.3333, "x": -100, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 115.25, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1.8333, "x": 7.62, "y": 0}]}, "Clavicle Right": {"rotate": [{"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 16.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 20, "curve": "stepped"}, {"time": 1.4, "angle": 20, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Arm Left": {"rotate": [{"time": 0, "angle": 0.13, "curve": "stepped"}, {"time": 0.1667, "angle": 0.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 8.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 0.13}]}, "Hip": {"translate": [{"time": 1.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 5, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 5, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Hand Right Scale": {"scale": [{"time": 1.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1.3, "y": 0.926, "curve": "stepped"}, {"time": 1.4, "x": 1.3, "y": 0.926, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 1, "y": 1}]}}, "ik": {"Forearm Left IK": [{"time": 0.1667, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "mix": 0, "bendPositive": false, "curve": "stepped"}, {"time": 0.8333, "mix": 0, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "bendPositive": false}]}, "transform": {"Card Hand Left": [{"time": 0, "rotateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": "stepped"}, {"time": 0.6667, "rotateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "rotateMix": 0, "scaleMix": 0, "shearMix": 0}], "Card hand Right": [{"time": 0.8333, "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "rotateMix": 0, "scaleMix": 0, "shearMix": 0}]}, "deform": {"default": {"Arm Left": {"Arm": [{"time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "offset": 28, "vertices": [1.62057, -0.43823, 1.75935, 0, 1.75935, 0, 1.75935, 0, 2.9482, 0.5834, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.75935], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333}]}, "Head": {"Head": [{"time": 1.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "offset": 128, "vertices": [-1.44913, 0, -1.44913, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0, 0, 0, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.44913, 0, -1.44913, 0, 0, 0, 0, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -1.82721, 0, -1.82721, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.82721, 0, -1.82721], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333}]}}}}, "TakeCard5": {"slots": {"Card 1": {"attachment": [{"time": 0.8333, "name": "Card"}, {"time": 1.5667, "name": null}]}, "Forearm Left": {"attachment": [{"time": 0.6667, "name": "Forearm 3"}, {"time": 0.8333, "name": "Forearm 1"}]}, "Forearm Left Shadow": {"color": [{"time": 0.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "color": "ffffffff"}]}, "Forearm Right Shadow": {"color": [{"time": 1.2333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffffff"}]}}, "bones": {"Hand Left": {"rotate": [{"time": 0, "angle": -22, "curve": "stepped"}, {"time": 0.3333, "angle": -22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 13.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -22}]}, "Forearm Left IK": {"translate": [{"time": 0, "x": -32.5, "y": 18}]}, "Finger Left 1": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.3333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -12}]}, "Finger Left 2": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.3333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -12}]}, "Finger Left 3": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.3333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -12}]}, "Finger Right 2": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 1, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Finger Right 3": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 1, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Forearm Left": {"rotate": [{"time": 0, "angle": -44.89, "curve": "stepped"}, {"time": 0.3333, "angle": -44.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 34.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -13.52}], "scale": [{"time": 0.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 0.882, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 1, "y": 1}]}, "Body 3D Horizontal": {"translate": [{"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -3.38, "y": -144.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 2.8, "y": 146.73, "curve": "stepped"}, {"time": 1.5667, "x": 2.8, "y": 146.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Body": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -4.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 5, "curve": "stepped"}, {"time": 1.5667, "angle": 5, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Clavicle Left": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 14.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0}]}, "Head 3D Horizontal": {"translate": [{"time": 0.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -173.4, "y": -13.88, "curve": "stepped"}, {"time": 1, "x": -173.4, "y": -13.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -9.04, "y": 318.72, "curve": "stepped"}, {"time": 1.5667, "x": -9.04, "y": 318.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Forearm Right IK": {"translate": [{"time": 0, "x": 32.5, "y": 18, "curve": "stepped"}, {"time": 0.3333, "x": 32.5, "y": 18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 47.26, "y": 8.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 22.5, "y": 10.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -68.36, "y": 20.32, "curve": "stepped"}, {"time": 1.5667, "x": -68.36, "y": 20.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 32.5, "y": 18}]}, "Hand Right": {"rotate": [{"time": 0, "angle": 22, "curve": "stepped"}, {"time": 0.3333, "angle": 22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 15.09, "curve": "stepped"}, {"time": 1, "angle": 15.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -3.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 22}]}, "Hip Bend Over": {"translate": [{"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 10, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -20, "y": 0, "curve": "stepped"}, {"time": 1.5667, "x": -20, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 12.53, "curve": "stepped"}, {"time": 1, "angle": 12.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -10, "curve": "stepped"}, {"time": 1.5667, "angle": -10, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Body Bend Over": {"translate": [{"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -29.6, "y": -2.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -35.17, "y": -0.52, "curve": "stepped"}, {"time": 1.5667, "x": -35.17, "y": -0.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Finger Right 1": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 1, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Chest": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1.12, "y": 1.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "Breast": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 3.91, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Body 3D Vertical": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 25, "y": -0.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head 3D Vertical": {"translate": [{"time": 0, "x": 7.62, "y": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.5, "x": -100, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 115.25, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2, "x": 7.62, "y": 0}]}, "Clavicle Right": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 16.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -30, "curve": "stepped"}, {"time": 1.5667, "angle": -30, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Arm Left": {"rotate": [{"time": 0, "angle": 0.13, "curve": "stepped"}, {"time": 0.3333, "angle": 0.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 8.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0.13}]}, "Hip": {"translate": [{"time": 1.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -7, "y": 0, "curve": "stepped"}, {"time": 1.5667, "x": -7, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}}, "ik": {"Forearm Left IK": [{"time": 0.3333, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "mix": 0, "bendPositive": false, "curve": "stepped"}, {"time": 1, "mix": 0, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "bendPositive": false}]}, "transform": {"Card Hand Left": [{"time": 0, "rotateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": "stepped"}, {"time": 0.8333, "rotateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "rotateMix": 0, "scaleMix": 0, "shearMix": 0}], "Card hand Right": [{"time": 1, "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "rotateMix": 0, "scaleMix": 0, "shearMix": 0}]}, "deform": {"default": {"Arm Left": {"Arm": [{"time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "offset": 28, "vertices": [1.62057, -0.43823, 1.75935, 0, 1.75935, 0, 1.75935, 0, 2.9482, 0.5834, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.75935], "curve": [0.25, 0, 0.75, 1]}, {"time": 2}]}, "Head": {"Head": [{"time": 1.5667, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "offset": 128, "vertices": [-1.44913, 0, -1.44913, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0, 0, 0, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.44913, 0, -1.44913, 0, 0, 0, 0, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -1.82721, 0, -1.82721, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.82721, 0, -1.82721], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9}]}}}}, "TakeCard6": {"slots": {"Card 1": {"attachment": [{"time": 0.8333, "name": "Card"}, {"time": 1.5667, "name": null}]}, "Forearm Left": {"attachment": [{"time": 0.6667, "name": "Forearm 3"}, {"time": 0.8333, "name": "Forearm 1"}]}, "Forearm Left Shadow": {"color": [{"time": 0.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "color": "ffffffff"}]}}, "bones": {"Hand Left": {"rotate": [{"time": 0, "angle": -22, "curve": "stepped"}, {"time": 0.3333, "angle": -22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 13.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -22}]}, "Forearm Left IK": {"translate": [{"time": 0, "x": -32.5, "y": 18}]}, "Finger Left 1": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.3333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -12}]}, "Finger Left 2": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.3333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -12}]}, "Finger Left 3": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.3333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -12}]}, "Finger Right 2": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 1, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Finger Right 3": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 1, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Forearm Left": {"rotate": [{"time": 0, "angle": -44.89, "curve": "stepped"}, {"time": 0.3333, "angle": -44.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 34.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -13.52}], "scale": [{"time": 0.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 0.882, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 1, "y": 1}]}, "Body 3D Horizontal": {"translate": [{"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -3.38, "y": -144.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -0.63, "y": -148.54, "curve": "stepped"}, {"time": 1.5667, "x": -0.63, "y": -148.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Body": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -4.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -2.81, "curve": "stepped"}, {"time": 1.5667, "angle": -2.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Clavicle Left": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 14.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0}]}, "Head 3D Horizontal": {"translate": [{"time": 0.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -173.4, "y": -13.88, "curve": "stepped"}, {"time": 1, "x": -173.4, "y": -13.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 2.71, "y": -227.53, "curve": "stepped"}, {"time": 1.5667, "x": 2.71, "y": -227.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Forearm Right IK": {"translate": [{"time": 0, "x": 32.5, "y": 18, "curve": "stepped"}, {"time": 0.3333, "x": 32.5, "y": 18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 47.26, "y": 8.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 41.07, "y": 7.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 35.4, "y": -4.11, "curve": "stepped"}, {"time": 1.5667, "x": 35.4, "y": -4.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 32.5, "y": 18}]}, "Hand Right": {"rotate": [{"time": 0, "angle": 22, "curve": "stepped"}, {"time": 0.3333, "angle": 22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 15.09, "curve": "stepped"}, {"time": 1, "angle": 15.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -3.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 22}]}, "Hip Bend Over": {"translate": [{"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 10, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -10, "y": 0, "curve": "stepped"}, {"time": 1.5667, "x": -10, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 12.53, "curve": "stepped"}, {"time": 1, "angle": 12.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 6, "curve": "stepped"}, {"time": 1.5667, "angle": 6, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Body Bend Over": {"translate": [{"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -29.6, "y": -2.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -47, "y": -0.44, "curve": "stepped"}, {"time": 1.5667, "x": -47, "y": -0.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Forearm Right": {"scale": [{"time": 1.2333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0.824, "y": 0.926, "curve": "stepped"}, {"time": 1.5667, "x": 0.824, "y": 0.926, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "Finger Right 1": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 1, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12}]}, "Chest": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1.12, "y": 1.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "Breast": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 3.91, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Body 3D Vertical": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 25, "y": -0.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head 3D Vertical": {"translate": [{"time": 0, "x": 7.62, "y": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.5, "x": -100, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 115.25, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2, "x": 7.62, "y": 0}]}, "Clavicle Right": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 16.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 20, "curve": "stepped"}, {"time": 1.5667, "angle": 20, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Arm Left": {"rotate": [{"time": 0, "angle": 0.13, "curve": "stepped"}, {"time": 0.3333, "angle": 0.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 8.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0.13}]}, "Hip": {"translate": [{"time": 1.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 5, "y": 0, "curve": "stepped"}, {"time": 1.5667, "x": 5, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Hand Right Scale": {"scale": [{"time": 1.2333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1.3, "y": 0.926, "curve": "stepped"}, {"time": 1.5667, "x": 1.3, "y": 0.926, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}}, "ik": {"Forearm Left IK": [{"time": 0.3333, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "mix": 0, "bendPositive": false, "curve": "stepped"}, {"time": 1, "mix": 0, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "bendPositive": false}]}, "transform": {"Card Hand Left": [{"time": 0, "rotateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": "stepped"}, {"time": 0.8333, "rotateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "rotateMix": 0, "scaleMix": 0, "shearMix": 0}], "Card hand Right": [{"time": 1, "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "rotateMix": 0, "scaleMix": 0, "shearMix": 0}]}, "deform": {"default": {"Arm Left": {"Arm": [{"time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "offset": 28, "vertices": [1.62057, -0.43823, 1.75935, 0, 1.75935, 0, 1.75935, 0, 2.9482, 0.5834, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.75935], "curve": [0.25, 0, 0.75, 1]}, {"time": 2}]}, "Head": {"Head": [{"time": 1.5667, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "offset": 128, "vertices": [-1.44913, 0, -1.44913, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0, 0, 0, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.44913, 0, -1.44913, 0, 0, 0, 0, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -1.82721, 0, -1.82721, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.82721, 0, -1.82721], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9}]}}}}, "TakeCardAll": {"slots": {"Card 1": {"attachment": [{"time": 0.8333, "name": "Card"}, {"time": 1.5667, "name": null}, {"time": 2.8333, "name": "Card"}, {"time": 3.5667, "name": null}, {"time": 4.6667, "name": "Card"}, {"time": 5.4, "name": null}, {"time": 6.5, "name": "Card"}, {"time": 7.2333, "name": null}]}, "Forearm Left": {"attachment": [{"time": 0.6667, "name": "Forearm 3"}, {"time": 0.8333, "name": "Forearm 1"}, {"time": 2.6667, "name": "Forearm 3"}, {"time": 2.8333, "name": "Forearm 1"}, {"time": 4.5, "name": "Forearm 3"}, {"time": 4.6667, "name": "Forearm 1"}, {"time": 6.3333, "name": "Forearm 3"}, {"time": 6.5, "name": "Forearm 1"}]}, "Forearm Left Shadow": {"color": [{"time": 0.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.2333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5, "color": "ffffffff", "curve": "stepped"}, {"time": 4.1667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 4.5, "color": "ffffff00", "curve": "stepped"}, {"time": 5.0667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 6, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 6.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 6.9, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1667, "color": "ffffffff"}]}, "Forearm Right": {"attachment": [{"time": 1.5, "name": "Forearm 2"}, {"time": 1.7, "name": "Forearm 1"}, {"time": 3.5, "name": "Forearm 2"}, {"time": 3.7, "name": "Forearm 1"}]}, "Forearm Right Shadow": {"color": [{"time": 1.2333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 5.0667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 5.4, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8333, "color": "ffffffff"}]}}, "bones": {"Arm Left": {"rotate": [{"time": 0, "angle": 0.13, "curve": "stepped"}, {"time": 0.3333, "angle": 0.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 8.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0.13, "curve": "stepped"}, {"time": 2.3333, "angle": 0.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "angle": 8.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2333, "angle": 0.13, "curve": "stepped"}, {"time": 4.1667, "angle": 0.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "angle": 8.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0667, "angle": 0.13, "curve": "stepped"}, {"time": 6, "angle": 0.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.5, "angle": 8.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.9, "angle": 0.13}]}, "Forearm Left": {"rotate": [{"time": 0, "angle": -44.89, "curve": "stepped"}, {"time": 0.3333, "angle": -44.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 34.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -13.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -44.89, "curve": "stepped"}, {"time": 2.3333, "angle": -44.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "angle": 34.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2333, "angle": -13.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "angle": -44.89, "curve": "stepped"}, {"time": 4.1667, "angle": -44.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "angle": 34.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0667, "angle": -13.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8333, "angle": -44.89, "curve": "stepped"}, {"time": 6, "angle": -44.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.5, "angle": 34.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.9, "angle": -13.52}], "scale": [{"time": 0.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 0.882, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "x": 0.882, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "x": 0.882, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.5, "x": 0.882, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.9, "x": 1, "y": 1}]}, "Card": {"scale": [{"time": 1.4333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "x": 1, "y": 0, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.4333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5667, "x": 1, "y": 0, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1}]}, "Hand Left": {"rotate": [{"time": 0, "angle": -22, "curve": "stepped"}, {"time": 0.3333, "angle": -22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 13.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -22, "curve": "stepped"}, {"time": 2.3333, "angle": -22, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "angle": 13.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2333, "angle": -18, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.6667, "angle": -22, "curve": "stepped"}, {"time": 4.1667, "angle": -22, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "angle": 13.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0667, "angle": -18, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.5, "angle": -22, "curve": "stepped"}, {"time": 6, "angle": -22, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.5, "angle": 13.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.9, "angle": -18, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.3333, "angle": -22}]}, "Forearm Left IK": {"translate": [{"time": 0, "x": -32.5, "y": 18}]}, "Finger Left 1": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.3333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -12, "curve": "stepped"}, {"time": 2.3333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2333, "angle": -12, "curve": "stepped"}, {"time": 4.1667, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0667, "angle": -12, "curve": "stepped"}, {"time": 6, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.9, "angle": -12}]}, "Finger Left 2": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.3333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -12, "curve": "stepped"}, {"time": 2.3333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2333, "angle": -12, "curve": "stepped"}, {"time": 4.1667, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0667, "angle": -12, "curve": "stepped"}, {"time": 6, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.9, "angle": -12}]}, "Finger Left 3": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 0.3333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -12, "curve": "stepped"}, {"time": 2.3333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2333, "angle": -12, "curve": "stepped"}, {"time": 4.1667, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0667, "angle": -12, "curve": "stepped"}, {"time": 6, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.9, "angle": -12}]}, "Head 3D Vertical": {"translate": [{"time": 0, "x": 7.62, "y": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.5, "x": -100, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 115.25, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2, "x": 7.62, "y": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2.5, "x": -100, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5, "x": 115.25, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 4, "x": 7.62, "y": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 4.3333, "x": -100, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "x": 115.25, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 5.8333, "x": 7.62, "y": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 6.1667, "x": -100, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1667, "x": 115.25, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 7.6667, "x": 7.62, "y": 0}]}, "Clavicle Right": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 16.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -15, "curve": "stepped"}, {"time": 1.5667, "angle": -15, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": 16.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5, "angle": 20, "curve": "stepped"}, {"time": 3.5667, "angle": 20, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "angle": 0, "curve": "stepped"}, {"time": 4.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.8333, "angle": 16.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "angle": 10, "curve": "stepped"}, {"time": 5.4, "angle": 10, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8333, "angle": 0, "curve": "stepped"}, {"time": 6, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.6667, "angle": 16.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.9, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1667, "angle": 20, "curve": "stepped"}, {"time": 7.2333, "angle": 20, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667, "angle": 0}]}, "Finger Right 2": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 1, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12, "curve": "stepped"}, {"time": 3, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.1667, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5667, "angle": 0, "curve": "stepped"}, {"time": 3.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "angle": -12, "curve": "stepped"}, {"time": 4.8333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 5, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.4, "angle": 0, "curve": "stepped"}, {"time": 5.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8333, "angle": -12, "curve": "stepped"}, {"time": 6.6667, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.8333, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.2333, "angle": 0, "curve": "stepped"}, {"time": 7.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667, "angle": -12}]}, "Hand Right": {"rotate": [{"time": 0, "angle": 22, "curve": "stepped"}, {"time": 0.3333, "angle": 22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 15.09, "curve": "stepped"}, {"time": 1, "angle": 15.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -3.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 22, "curve": "stepped"}, {"time": 2.3333, "angle": 22, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "angle": 15.09, "curve": "stepped"}, {"time": 3, "angle": 15.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2333, "angle": -3.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "angle": 22, "curve": "stepped"}, {"time": 4.1667, "angle": 22, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "angle": 15.09, "curve": "stepped"}, {"time": 4.8333, "angle": 15.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0667, "angle": -3.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8333, "angle": 22, "curve": "stepped"}, {"time": 6, "angle": 22, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.5, "angle": 15.09, "curve": "stepped"}, {"time": 6.6667, "angle": 15.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.9, "angle": -3.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667, "angle": 22}]}, "Body 3D Horizontal": {"translate": [{"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -3.38, "y": -144.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -0.63, "y": 90.54, "curve": "stepped"}, {"time": 1.5667, "x": -0.63, "y": 90.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "x": -3.38, "y": -144.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5, "x": -0.63, "y": -148.54, "curve": "stepped"}, {"time": 3.5667, "x": -0.63, "y": -148.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "x": -3.38, "y": -144.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.5, "x": -3.38, "y": -144.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.9, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1667, "x": -0.63, "y": -148.54, "curve": "stepped"}, {"time": 7.2333, "x": -0.63, "y": -148.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667, "x": 0, "y": 0}]}, "Body": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -4.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 3.5, "curve": "stepped"}, {"time": 1.5667, "angle": 3.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "angle": -4.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5, "angle": -2.81, "curve": "stepped"}, {"time": 3.5667, "angle": -2.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "angle": 0, "curve": "stepped"}, {"time": 4.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "angle": -4.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0667, "angle": 0, "curve": "stepped"}, {"time": 6, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.5, "angle": -4.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.9, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1667, "angle": -2.81, "curve": "stepped"}, {"time": 7.2333, "angle": -2.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667, "angle": 0}]}, "Clavicle Left": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 14.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "angle": 14.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2333, "angle": 0, "curve": "stepped"}, {"time": 4.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "angle": 14.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0667, "angle": 0, "curve": "stepped"}, {"time": 6, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.5, "angle": 14.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.9, "angle": 0}]}, "Head": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 12.53, "curve": "stepped"}, {"time": 1, "angle": 12.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -6, "curve": "stepped"}, {"time": 1.5667, "angle": -6, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "angle": 12.53, "curve": "stepped"}, {"time": 3, "angle": 12.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5, "angle": 6, "curve": "stepped"}, {"time": 3.5667, "angle": 6, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "angle": 0, "curve": "stepped"}, {"time": 4.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "angle": 12.53, "curve": "stepped"}, {"time": 4.8333, "angle": 12.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "angle": 0, "curve": "stepped"}, {"time": 6, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.5, "angle": 12.53, "curve": "stepped"}, {"time": 6.6667, "angle": 12.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1667, "angle": 6, "curve": "stepped"}, {"time": 7.2333, "angle": 6, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667, "angle": 0}]}, "Head 3D Horizontal": {"translate": [{"time": 0.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -173.4, "y": -13.88, "curve": "stepped"}, {"time": 1, "x": -173.4, "y": -13.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0, "y": 197.8, "curve": "stepped"}, {"time": 1.5667, "x": 0, "y": 197.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "x": -173.4, "y": -13.88, "curve": "stepped"}, {"time": 3, "x": -173.4, "y": -13.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5, "x": 2.71, "y": -227.53, "curve": "stepped"}, {"time": 3.5667, "x": 2.71, "y": -227.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "x": -173.4, "y": -13.88, "curve": "stepped"}, {"time": 4.8333, "x": -173.4, "y": -13.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "x": 0, "y": 69.76, "curve": "stepped"}, {"time": 5.4, "x": 0, "y": 69.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.5, "x": -173.4, "y": -13.88, "curve": "stepped"}, {"time": 6.6667, "x": -173.4, "y": -13.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1667, "x": 2.71, "y": -227.53, "curve": "stepped"}, {"time": 7.2333, "x": 2.71, "y": -227.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667, "x": 0, "y": 0}]}, "Finger Right 3": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 1, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12, "curve": "stepped"}, {"time": 3, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.1667, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5667, "angle": 0, "curve": "stepped"}, {"time": 3.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "angle": -12, "curve": "stepped"}, {"time": 4.8333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 5, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.4, "angle": 0, "curve": "stepped"}, {"time": 5.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8333, "angle": -12, "curve": "stepped"}, {"time": 6.6667, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.8333, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.2333, "angle": 0, "curve": "stepped"}, {"time": 7.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667, "angle": -12}]}, "Forearm Right IK": {"translate": [{"time": 0, "x": 32.5, "y": 18, "curve": "stepped"}, {"time": 0.3333, "x": 32.5, "y": 18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 47.26, "y": 8.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 41.07, "y": 7.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -36.43, "y": 10.48, "curve": "stepped"}, {"time": 1.5667, "x": -36.43, "y": 10.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 32.5, "y": 18, "curve": "stepped"}, {"time": 2.3333, "x": 32.5, "y": 18, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": 47.26, "y": 8.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2333, "x": 41.07, "y": 7.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5, "x": 35.4, "y": -4.11, "curve": "stepped"}, {"time": 3.5667, "x": 35.4, "y": -4.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "x": 32.5, "y": 18, "curve": "stepped"}, {"time": 4.1667, "x": 32.5, "y": 18, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.8333, "x": 47.26, "y": 8.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0667, "x": 22.5, "y": 10.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "x": 7.07, "y": 0.07, "curve": "stepped"}, {"time": 5.4, "x": 7.07, "y": 0.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8333, "x": 32.5, "y": 18, "curve": "stepped"}, {"time": 6, "x": 32.5, "y": 18, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.6667, "x": 47.26, "y": 8.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.9, "x": 41.07, "y": 7.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1667, "x": 35.4, "y": -4.11, "curve": "stepped"}, {"time": 7.2333, "x": 35.4, "y": -4.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667, "x": 32.5, "y": 18}]}, "Hip Bend Over": {"translate": [{"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 10, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -10, "y": 0, "curve": "stepped"}, {"time": 1.5667, "x": -10, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "x": 10, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5, "x": -10, "y": 0, "curve": "stepped"}, {"time": 3.5667, "x": -10, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "x": 10, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.5, "x": 10, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.9, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1667, "x": -10, "y": 0, "curve": "stepped"}, {"time": 7.2333, "x": -10, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667, "x": 0, "y": 0}]}, "Body Bend Over": {"translate": [{"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -29.6, "y": -2.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -44.24, "y": -0.46, "curve": "stepped"}, {"time": 1.5667, "x": -44.24, "y": -0.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "x": -29.6, "y": -2.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5, "x": -47, "y": -0.44, "curve": "stepped"}, {"time": 3.5667, "x": -47, "y": -0.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "x": -29.6, "y": -2.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "x": -50, "y": 0.32, "curve": "stepped"}, {"time": 5.4, "x": -50, "y": 0.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.5, "x": -29.6, "y": -2.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.9, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1667, "x": -47, "y": -0.44, "curve": "stepped"}, {"time": 7.2333, "x": -47, "y": -0.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667, "x": 0, "y": 0}]}, "Forearm Right": {"scale": [{"time": 1.2333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0.824, "y": 0.926, "curve": "stepped"}, {"time": 1.5667, "x": 0.824, "y": 0.926, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.2333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5, "x": 0.824, "y": 0.926, "curve": "stepped"}, {"time": 3.5667, "x": 0.824, "y": 0.926, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "x": 0.765, "y": 1, "curve": "stepped"}, {"time": 5.4, "x": 0.765, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6.9, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1667, "x": 0.824, "y": 0.926, "curve": "stepped"}, {"time": 7.2333, "x": 0.824, "y": 0.926, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667, "x": 1, "y": 1}]}, "Finger Right 1": {"rotate": [{"time": 0, "angle": -12, "curve": "stepped"}, {"time": 1, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -12, "curve": "stepped"}, {"time": 3, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.1667, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5667, "angle": 0, "curve": "stepped"}, {"time": 3.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "angle": -12, "curve": "stepped"}, {"time": 4.8333, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 5, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.4, "angle": 0, "curve": "stepped"}, {"time": 5.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8333, "angle": -12, "curve": "stepped"}, {"time": 6.6667, "angle": -12, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.8333, "angle": 8, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.2333, "angle": 0, "curve": "stepped"}, {"time": 7.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667, "angle": -12}], "scale": [{"time": 1.4, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0.15, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 0.15, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.4, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5, "x": 0.15, "y": 1, "curve": "stepped"}, {"time": 3.6667, "x": 0.15, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.7667, "x": 1, "y": 1}]}, "Chest": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1.12, "y": 1.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": 1.12, "y": 1.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.8333, "x": 1.12, "y": 1.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.6667, "x": 1.12, "y": 1.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667, "x": 1, "y": 1}]}, "Breast": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 3.91, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": 3.91, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.8333, "x": 3.91, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.6667, "x": 3.91, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667, "x": 0, "y": 0}]}, "Body 3D Vertical": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 25, "y": -0.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": 25, "y": -0.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.8333, "x": 25, "y": -0.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.6667, "x": 25, "y": -0.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667, "x": 0, "y": 0}]}, "Hand Right Scale": {"scale": [{"time": 1.2333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1.3, "y": 0.926, "curve": "stepped"}, {"time": 1.5667, "x": 1.3, "y": 0.926, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.2333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5, "x": 1.3, "y": 0.926, "curve": "stepped"}, {"time": 3.5667, "x": 1.3, "y": 0.926, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "x": 1.35, "y": 0.926, "curve": "stepped"}, {"time": 5.4, "x": 1.35, "y": 0.926, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6.9, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1667, "x": 1.3, "y": 0.926, "curve": "stepped"}, {"time": 7.2333, "x": 1.3, "y": 0.926, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667, "x": 1, "y": 1}]}}, "ik": {"Forearm Left IK": [{"time": 0.3333, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "mix": 0, "bendPositive": false, "curve": "stepped"}, {"time": 1, "mix": 0, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "bendPositive": false, "curve": "stepped"}, {"time": 2.3333, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "mix": 0, "bendPositive": false, "curve": "stepped"}, {"time": 3, "mix": 0, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5667, "bendPositive": false, "curve": "stepped"}, {"time": 4.1667, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.5, "mix": 0, "bendPositive": false, "curve": "stepped"}, {"time": 4.8333, "mix": 0, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.4, "bendPositive": false, "curve": "stepped"}, {"time": 6, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.3333, "mix": 0, "bendPositive": false, "curve": "stepped"}, {"time": 6.6667, "mix": 0, "bendPositive": false, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.2333, "bendPositive": false}]}, "transform": {"Card Hand Left": [{"time": 0, "rotateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": "stepped"}, {"time": 0.8333, "rotateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "rotateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": "stepped"}, {"time": 2.8333, "rotateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2333, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5, "rotateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": "stepped"}, {"time": 4.6667, "rotateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0667, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "rotateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": "stepped"}, {"time": 6.5, "rotateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.9, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1667, "rotateMix": 0, "scaleMix": 0, "shearMix": 0}], "Card hand Right": [{"time": 1, "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "rotateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": "stepped"}, {"time": 1.6667, "rotateMix": 0, "scaleMix": 0, "shearMix": 0}, {"time": 2, "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": "stepped"}, {"time": 3, "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2333, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5, "rotateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": "stepped"}, {"time": 3.6667, "rotateMix": 0, "scaleMix": 0, "shearMix": 0}, {"time": 4, "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": "stepped"}, {"time": 4.8333, "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0667, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "rotateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": "stepped"}, {"time": 5.5, "rotateMix": 0, "scaleMix": 0, "shearMix": 0}, {"time": 5.8333, "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": "stepped"}, {"time": 6.6667, "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.9, "scaleMix": 0, "shearMix": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1667, "rotateMix": 0, "scaleMix": 0, "shearMix": 0}]}, "deform": {"default": {"Arm Left": {"Arm": [{"time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "offset": 28, "vertices": [1.62057, -0.43823, 1.75935, 0, 1.75935, 0, 1.75935, 0, 2.9482, 0.5834, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.75935], "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "curve": "stepped"}, {"time": 2.3333, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "offset": 28, "vertices": [1.62057, -0.43823, 1.75935, 0, 1.75935, 0, 1.75935, 0, 2.9482, 0.5834, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.75935], "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "curve": "stepped"}, {"time": 4.1667, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "offset": 28, "vertices": [1.62057, -0.43823, 1.75935, 0, 1.75935, 0, 1.75935, 0, 2.9482, 0.5834, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.75935], "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8333, "curve": "stepped"}, {"time": 6, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.5, "offset": 28, "vertices": [1.62057, -0.43823, 1.75935, 0, 1.75935, 0, 1.75935, 0, 2.9482, 0.5834, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.75935], "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667}]}, "Forearm Right Shadow": {"Forearm Shadow": [{"time": 0, "curve": "stepped"}, {"time": 1.5, "offset": 26, "vertices": [-3.61166, -3.36191, -3.65649, -3.313, -7.99842, -3.12586, -8.04015, -3.01786, -12.12449, -1.94302, -12.14941, -1.77921, -16.90559, 0.92504, -16.90559, 0.92504, -12.12449, -1.94302, -12.14941, -1.77921, -7.99842, -3.12586, -8.04015, -3.01786, -3.61166, -3.36191, -3.65649, -3.313], "curve": "stepped"}, {"time": 1.7, "curve": "stepped"}, {"time": 2, "curve": "stepped"}, {"time": 3.5, "offset": 26, "vertices": [-3.61166, -3.36191, -3.65649, -3.313, -7.99842, -3.12586, -8.04015, -3.01786, -12.12449, -1.94302, -12.14941, -1.77921, -16.90559, 0.92504, -16.90559, 0.92504, -12.12449, -1.94302, -12.14941, -1.77921, -7.99842, -3.12586, -8.04015, -3.01786, -3.61166, -3.36191, -3.65649, -3.313], "curve": "stepped"}, {"time": 3.7}]}, "Head": {"Head": [{"time": 1.5667, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "offset": 128, "vertices": [-1.44913, 0, -1.44913, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0, 0, 0, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.44913, 0, -1.44913, 0, 0, 0, 0, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -1.82721, 0, -1.82721, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.82721, 0, -1.82721], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9, "curve": "stepped"}, {"time": 3.5667, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.7333, "offset": 128, "vertices": [-1.44913, 0, -1.44913, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0, 0, 0, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.44913, 0, -1.44913, 0, 0, 0, 0, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -1.82721, 0, -1.82721, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.82721, 0, -1.82721], "curve": [0.25, 0, 0.75, 1]}, {"time": 3.9, "curve": "stepped"}, {"time": 5.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.5667, "offset": 128, "vertices": [-1.44913, 0, -1.44913, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0, 0, 0, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.44913, 0, -1.44913, 0, 0, 0, 0, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -1.82721, 0, -1.82721, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.82721, 0, -1.82721], "curve": [0.25, 0, 0.75, 1]}, {"time": 5.7333, "curve": "stepped"}, {"time": 7.2333, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.4, "offset": 128, "vertices": [-1.44913, 0, -1.44913, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0.50391, 0, 0, 0, 0, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, -3.90619, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, -3.65414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.44913, 0, -1.44913, 0, 0, 0, 0, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -3.78046, 0, -1.82721, 0, -1.82721, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, 0.62988, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, -3.96912, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.82721, 0, -1.82721], "curve": [0.25, 0, 0.75, 1]}, {"time": 7.5667}]}}}}}}
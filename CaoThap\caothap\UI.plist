<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>aActive.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{43,47}</string>
                <key>spriteSourceSize</key>
                <string>{43,47}</string>
                <key>textureRect</key>
                <string>{{306,44},{43,47}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{800,522}</string>
                <key>spriteSourceSize</key>
                <string>{800,522}</string>
                <key>textureRect</key>
                <string>{{0,309},{800,522}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bgBlack.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{221,202}</string>
                <key>spriteSourceSize</key>
                <string>{221,202}</string>
                <key>textureRect</key>
                <string>{{639,107},{221,202}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bichD.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{22,23}</string>
                <key>spriteSourceSize</key>
                <string>{22,23}</string>
                <key>textureRect</key>
                <string>{{0,0},{22,23}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bichU.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{22,23}</string>
                <key>spriteSourceSize</key>
                <string>{22,23}</string>
                <key>textureRect</key>
                <string>{{22,0},{22,23}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>black.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{128,128}</string>
                <key>spriteSourceSize</key>
                <string>{128,128}</string>
                <key>textureRect</key>
                <string>{{511,107},{128,128}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>boderCuoc.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{142,39}</string>
                <key>spriteSourceSize</key>
                <string>{142,39}</string>
                <key>textureRect</key>
                <string>{{44,0},{142,39}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn100KOff.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{102,44}</string>
                <key>spriteSourceSize</key>
                <string>{102,44}</string>
                <key>textureRect</key>
                <string>{{186,0},{102,44}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn100KOn.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{102,44}</string>
                <key>spriteSourceSize</key>
                <string>{102,44}</string>
                <key>textureRect</key>
                <string>{{288,0},{102,44}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn10KOff.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{102,44}</string>
                <key>spriteSourceSize</key>
                <string>{102,44}</string>
                <key>textureRect</key>
                <string>{{390,0},{102,44}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn10KOn.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{102,44}</string>
                <key>spriteSourceSize</key>
                <string>{102,44}</string>
                <key>textureRect</key>
                <string>{{492,0},{102,44}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn1KOff.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{102,44}</string>
                <key>spriteSourceSize</key>
                <string>{102,44}</string>
                <key>textureRect</key>
                <string>{{594,0},{102,44}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn1KOn.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{102,44}</string>
                <key>spriteSourceSize</key>
                <string>{102,44}</string>
                <key>textureRect</key>
                <string>{{696,0},{102,44}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn500KOff.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{102,44}</string>
                <key>spriteSourceSize</key>
                <string>{102,44}</string>
                <key>textureRect</key>
                <string>{{798,0},{102,44}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn500KOn.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{102,44}</string>
                <key>spriteSourceSize</key>
                <string>{102,44}</string>
                <key>textureRect</key>
                <string>{{0,44},{102,44}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn50KOff.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{102,44}</string>
                <key>spriteSourceSize</key>
                <string>{102,44}</string>
                <key>textureRect</key>
                <string>{{102,44},{102,44}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn50KOn.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{102,44}</string>
                <key>spriteSourceSize</key>
                <string>{102,44}</string>
                <key>textureRect</key>
                <string>{{204,44},{102,44}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btnBatDau.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{199,53}</string>
                <key>spriteSourceSize</key>
                <string>{199,53}</string>
                <key>textureRect</key>
                <string>{{349,44},{199,53}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btnDownOff.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{74,60}</string>
                <key>spriteSourceSize</key>
                <string>{74,60}</string>
                <key>textureRect</key>
                <string>{{622,44},{74,60}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btnDownOn.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{74,59}</string>
                <key>spriteSourceSize</key>
                <string>{74,59}</string>
                <key>textureRect</key>
                <string>{{548,44},{74,59}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btnLuotMoi.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{129,122}</string>
                <key>spriteSourceSize</key>
                <string>{129,122}</string>
                <key>textureRect</key>
                <string>{{253,107},{129,122}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btnLuotMoiOff.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{129,122}</string>
                <key>spriteSourceSize</key>
                <string>{129,122}</string>
                <key>textureRect</key>
                <string>{{382,107},{129,122}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btnUpOff.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{74,60}</string>
                <key>spriteSourceSize</key>
                <string>{74,60}</string>
                <key>textureRect</key>
                <string>{{696,44},{74,60}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btnUpOn.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{74,60}</string>
                <key>spriteSourceSize</key>
                <string>{74,60}</string>
                <key>textureRect</key>
                <string>{{770,44},{74,60}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icoExitOn.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{63,63}</string>
                <key>spriteSourceSize</key>
                <string>{63,63}</string>
                <key>textureRect</key>
                <string>{{844,44},{63,63}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icoLichSuCuocOn.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{63,64}</string>
                <key>spriteSourceSize</key>
                <string>{63,64}</string>
                <key>textureRect</key>
                <string>{{126,107},{63,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icoLuatChoiOn.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{64,64}</string>
                <key>spriteSourceSize</key>
                <string>{64,64}</string>
                <key>textureRect</key>
                <string>{{189,107},{64,64}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icoTopOn.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{63,63}</string>
                <key>spriteSourceSize</key>
                <string>{63,63}</string>
                <key>textureRect</key>
                <string>{{0,107},{63,63}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>iconLSH.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{63,63}</string>
                <key>spriteSourceSize</key>
                <string>{63,63}</string>
                <key>textureRect</key>
                <string>{{63,107},{63,63}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>UI.png</string>
            <key>size</key>
            <string>{907,831}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:2b7b386e8fddd3aea8429c7be69b6bd3:a9736de449a59aefea8c786ad0cb2fad:4d1c460786d5294715f9f3e199a00761$</string>
            <key>textureFileName</key>
            <string>UI.png</string>
        </dict>
    </dict>
</plist>

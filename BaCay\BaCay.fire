[{"__type__": "cc.SceneAsset", "_name": "", "_objFlags": 0, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 123}], "_active": false, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_is3DNode": true, "_groupIndex": 0, "groupIndex": 0, "autoReleaseAssets": false, "_id": "26ad799e-5475-4615-8123-b8bac7816069"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 5}], "_active": true, "_components": [{"__id__": 127}, {"__id__": 128}, {"__id__": 129}, {"__id__": 130}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [640, 360, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "30hClvjS1DrpLbRIWyRUl5"}, {"__type__": "cc.Node", "_name": "Main Camera", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 623.5382907247958, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c6ktCiYXpOpp5+B3s9mye/"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_cullingMask": 4294967295, "_clearFlags": 7, "_backgroundColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": -1, "_zoomRatio": 1, "_targetTexture": null, "_fov": 60, "_orthoSize": 10, "_nearClip": 1, "_farClip": 4096, "_ortho": true, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_renderStages": 1, "_alignWithScreen": true, "_id": "25khgRzIRBWZVEpOvzgJUm"}, {"__type__": "cc.Node", "_name": "Game", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 6}], "_active": true, "_components": [{"__id__": 126}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "9dyK/yRylGwplb/3pxbL3R"}, {"__type__": "cc.Node", "_name": "UI_Room", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [{"__id__": 7}, {"__id__": 10}, {"__id__": 49}, {"__id__": 62}, {"__id__": 70}, {"__id__": 86}, {"__id__": 94}, {"__id__": 101}], "_active": true, "_components": [{"__id__": 118}, {"__id__": 119}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "dc7cixq8xLGpho4a8E5T2o"}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [{"__id__": 8}, {"__id__": 9}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "48yl0YRt9EDJ/GdmTcbFr6"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "65e9a464-1f02-4100-9eab-0d17bb6df800"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "f8d835f9-31d3-4e33-91ab-d8dca1fe6925"}, "_id": "84CTJkevhK/YMYK7q4Croz"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1280, "_originalHeight": 720, "_id": "e8MHPpJ+9CrqyRWxZVLREz"}, {"__type__": "cc.Node", "_name": "top", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [{"__id__": 11}, {"__id__": 22}, {"__id__": 40}], "_active": true, "_components": [{"__id__": 47}, {"__id__": 48}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 93.3333333}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 313.265, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "35mNR3OUpOs7Ynys8eL0wX"}, {"__type__": "cc.Node", "_name": "btnHideFull", "_objFlags": 0, "_parent": {"__id__": 10}, "_children": [{"__id__": 12}, {"__id__": 14}, {"__id__": 16}], "_active": true, "_components": [{"__id__": 19}, {"__id__": 21}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [314.966, 5.52, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "89SKezBNxCJbdSG8AO1ZDz"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 0, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [{"__id__": 13}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-50.224, -1.343, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "fcKue6y6lIPqmdjfPSFZEX"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "13c73131-d663-469e-b60a-6f9f45a21c62"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "f8d835f9-31d3-4e33-91ab-d8dca1fe6925"}, "_id": "edAr87iDxOsb+rOQ39hWvx"}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 0, "_parent": {"__id__": 11}, "_children": [], "_active": false, "_components": [{"__id__": 15}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-50, -1.79, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1dawatkfRLHLOOavBx0i13"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ddc53f62-48a4-421e-80a5-beb53652ea1e"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "f8d835f9-31d3-4e33-91ab-d8dca1fe6925"}, "_id": "36qb/gLXVGLbNT7csAlFJA"}, {"__type__": "cc.Node", "_name": "lbHideTable", "_objFlags": 0, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [{"__id__": 17}, {"__id__": 18}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 197, "g": 179, "b": 128, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 90.18, "height": 25.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-24.577, -0.699, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "747eWXgXFCdquN6aZMiPAx"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Ẩn bàn full", "_N$string": "Ẩn bàn full", "_fontSize": 20, "_lineHeight": 20, "_enableWrapText": false, "_N$file": {"__uuid__": "d2505d39-c061-4ba3-a77b-aa0e8e141d95"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "11bfwvECJMQqCNJk0dEAhE"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "id": "txt_hide_full_room", "isUpperCase": false, "_id": "e5dmDJ9/9J+KdQvSQnkfRd"}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 12}, "_N$isChecked": false, "toggleGroup": null, "checkMark": {"__id__": 15}, "checkEvents": [{"__id__": 20}], "_id": "6cvvCZQoBIhY7lJSSxJlZ3"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 6}, "component": "", "_componentId": "bef07HH7BlP/btnMHOsS2YX", "handler": "hideRoomFull", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 32, "_left": 0, "_right": 250.034, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "f6urFlpRlHdbCbrW+xWQCp"}, {"__type__": "cc.Node", "_name": "EditBox Find Room", "_objFlags": 0, "_parent": {"__id__": 10}, "_children": [{"__id__": 23}, {"__id__": 26}, {"__id__": 29}, {"__id__": 33}], "_active": true, "_components": [{"__id__": 38}, {"__id__": 39}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [517.257, 4.533, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "0b2MK9uu5OrJDuaBt/qM30"}, {"__type__": "cc.Node", "_name": "BACKGROUND_SPRITE", "_objFlags": 0, "_parent": {"__id__": 22}, "_children": [], "_active": true, "_components": [{"__id__": 24}, {"__id__": 25}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "636PAiMR5J9KS29TwKK5ZO"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "dbedcd11-13b1-4845-8f3c-b54062eaf971"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "f8d835f9-31d3-4e33-91ab-d8dca1fe6925"}, "_id": "17PgRBDfVD47so0yS3TcqI"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": -10, "_right": -10, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 160, "_originalHeight": 40, "_id": "53SP7eD01J/IcN0ruyu5BL"}, {"__type__": "cc.Node", "_name": "TEXT_LABEL", "_objFlags": 0, "_parent": {"__id__": 22}, "_children": [], "_active": false, "_components": [{"__id__": 27}, {"__id__": 28}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 178, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-88, 22.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "23qt6M+GJD55mfu9MZ1TWG"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 20, "_lineHeight": 25, "_enableWrapText": false, "_N$file": {"__uuid__": "d2505d39-c061-4ba3-a77b-aa0e8e141d95"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": "6c3HClcvxB5IyBHo96//PV"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 2, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 158, "_originalHeight": 40, "_id": "33FWg9tdNMpbHUxpfW7D8r"}, {"__type__": "cc.Node", "_name": "PLACEHOLDER_LABEL", "_objFlags": 0, "_parent": {"__id__": 22}, "_children": [], "_active": true, "_components": [{"__id__": 30}, {"__id__": 31}, {"__id__": 32}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 197, "g": 179, "b": 128, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 178, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-81.748, 22.689, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "44GTnjKW1IrJV+pQErZFKc"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Nhập ID bàn ...", "_N$string": "Nhập ID bàn ...", "_fontSize": 20, "_lineHeight": 25, "_enableWrapText": false, "_N$file": {"__uuid__": "d2505d39-c061-4ba3-a77b-aa0e8e141d95"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": "d4HNJ2w2NCgYzGaSUCvacK"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 8.251999999999995, "_right": -6.251999999999995, "_top": -0.18900000000000006, "_bottom": 0.18900000000000006, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 158, "_originalHeight": 40, "_id": "27Nek3mAtHua+onIlgPANH"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "id": "txt_enter_room_id", "isUpperCase": false, "_id": "00c22NvwxMcYi5mwJgB6ak"}, {"__type__": "cc.Node", "_name": "btn_Search", "_objFlags": 0, "_parent": {"__id__": 22}, "_children": [{"__id__": 34}], "_active": true, "_components": [{"__id__": 36}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 45, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [76.964, -0.224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "17utOjBu9CUIvXxiuGiYDU"}, {"__type__": "cc.Node", "_name": "ic_tim<PERSON>m", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [], "_active": true, "_components": [{"__id__": 35}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "4cZm7LGxRAwIJL4Ro+iuD2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "0e0b22f1-efbc-40f9-89ca-c81c068af09e"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "f8d835f9-31d3-4e33-91ab-d8dca1fe6925"}, "_id": "a3bKG88hJEu6XCiAGXtkEp"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 37}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 1, "transition": 1, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": "99iuU4dktD8LZvAW9GYei/"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 6}, "component": "", "_componentId": "bef07HH7BlP/btnMHOsS2YX", "handler": "findRoomId", "customEventData": ""}, {"__type__": "cc.EditBox", "_name": "", "_objFlags": 0, "node": {"__id__": 22}, "_enabled": true, "_string": "", "returnType": 0, "maxLength": 4, "_tabIndex": 0, "editingDidBegan": [], "textChanged": [], "editingDidEnded": [], "editingReturn": [], "_N$textLabel": {"__id__": 27}, "_N$placeholderLabel": {"__id__": 30}, "_N$background": {"__id__": 24}, "_N$inputFlag": 5, "_N$inputMode": 2, "_N$stayOnTop": false, "_id": "c8l4tz8SJHa4L36PqfSu9X"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 22}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 32, "_left": 0, "_right": 32.74300000000005, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "6bPZ3e9jtPAqCvwn42UrMd"}, {"__type__": "cc.Node", "_name": "bg_back_btn", "_objFlags": 0, "_parent": {"__id__": 10}, "_children": [{"__id__": 41}], "_active": true, "_components": [{"__id__": 46}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 138.3333333, "height": 73.8888889}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-588.449, 8.439, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "7bJZJ7YEdGjpXvPcAHmOaO"}, {"__type__": "cc.Node", "_name": "btnBack", "_objFlags": 0, "_parent": {"__id__": 40}, "_children": [], "_active": true, "_components": [{"__id__": 42}, {"__id__": 43}, {"__id__": 44}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 59.3333333, "height": 52}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3.552713678800501e-15, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c7HOak1nVPh5B4d1eKGO8G"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9db43530-9160-4d6e-950f-520afae15cf0"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": {"__uuid__": "f8d835f9-31d3-4e33-91ab-d8dca1fe6925"}, "_id": "12yLb74BlIN717l30lrcpr"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 9, "_left": 39.5, "_right": 0, "_top": 10.944444449999999, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "f1m8QWS99HOLLK4/thgHER"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 45}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": "2d6eSIBpdFgJOaF0kQ/+CT"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 6}, "component": "", "_componentId": "bef07HH7BlP/btnMHOsS2YX", "handler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 40}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 8, "_left": -17.61566664999995, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "d1uIm6ngRIo7P2FJkVmzLM"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "34edb958-0d52-4f7f-b949-e6d56a8c56c7"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "f8d835f9-31d3-4e33-91ab-d8dca1fe6925"}, "_id": "73iJjZTkZM169eY7ULQtzN"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 41, "_left": 0, "_right": 0, "_top": 0.06833334999998897, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 455, "_originalHeight": 0, "_id": "2eIkL1KXVJhaSaP9nuI62x"}, {"__type__": "cc.Node", "_name": "Top Left", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [{"__id__": 50}, {"__id__": 52}, {"__id__": 54}], "_active": true, "_components": [{"__id__": 60}, {"__id__": 61}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 480, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-400, 315, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "4dRYx252pLdKMORz5Mx0C5"}, {"__type__": "cc.Node", "_name": "spr<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 49}, "_children": [], "_active": true, "_components": [{"__id__": 51}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-97.36, -1.318, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1bGPoEuM1OgqO2NanBFv/Z"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5a38671f-b6e2-427f-bdb4-bc339e9e2e31"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "df4b779c-3864-4fc9-ad7c-11e6b3b25e7c"}, "_id": "42bQLSCYFKO61kBiypDTtb"}, {"__type__": "cc.Node", "_name": "lblNickname", "_objFlags": 0, "_parent": {"__id__": 49}, "_children": [], "_active": true, "_components": [{"__id__": 53}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 85, "height": 25.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-43.472, 28.641, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "51wXsI0p9HTLA5eJm7iqrj"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "userName", "_N$string": "userName", "_fontSize": 20, "_lineHeight": 20, "_enableWrapText": false, "_N$file": {"__uuid__": "d2505d39-c061-4ba3-a77b-aa0e8e141d95"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "33ffhKvYJO44cN1II3M3b7"}, {"__type__": "cc.Node", "_name": "BG", "_objFlags": 0, "_parent": {"__id__": 49}, "_children": [{"__id__": 55}, {"__id__": 57}], "_active": true, "_components": [{"__id__": 59}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 202.3, "height": 38.7}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [49.368, -4.1, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b25RNxBy9E/LsrDtaDqt0e"}, {"__type__": "cc.Node", "_name": "ic", "_objFlags": 0, "_parent": {"__id__": 54}, "_children": [], "_active": true, "_components": [{"__id__": 56}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-81.947, 0.167, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d1W+wh+55KV4qf9TTcgGtG"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "69db57e2-8242-4139-99c2-17d014b94108"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "88aVURzDpFp56h5Ickr3HD"}, {"__type__": "cc.Node", "_name": "lblCoin", "_objFlags": 0, "_parent": {"__id__": 54}, "_children": [], "_active": true, "_components": [{"__id__": 58}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 19.38, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-61.93, 16.42, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "04fl8xCo1BWp+oxz9m7Xt8"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "0", "_N$string": "0", "_fontSize": 20, "_lineHeight": 20, "_enableWrapText": false, "_N$file": {"__uuid__": "c5cc6111-131a-4599-9d0d-c2cedc3501c1"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "69YoRa1JJEF62WlFCr5Wab"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 54}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "dbedcd11-13b1-4845-8f3c-b54062eaf971"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "f8d835f9-31d3-4e33-91ab-d8dca1fe6925"}, "_id": "9bYW1b25RD860rYg/uJC1x"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 9, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "f7sIV2e7BHGJj6bfUb6k7O"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "3fh4He0k9F15uBBqEphesK"}, {"__type__": "cc.Node", "_name": "ScrollView Rooms", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [{"__id__": 63}], "_active": true, "_components": [{"__id__": 68}, {"__id__": 69}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1000, "height": 410}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -25.959000000000003, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f4ph7amaZNN7FVcjRogyiK"}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 62}, "_children": [{"__id__": 64}], "_active": true, "_components": [{"__id__": 66}, {"__id__": 67}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1000, "height": 410}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1bxiYFfltCIpzNxsEctQCx"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 63}, "_children": [], "_active": true, "_components": [{"__id__": 65}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 914, "height": 148.9}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2.872, 205, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c3XRDp07hDQ5wocttPZ3vo"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 64}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 914, "height": 148.9}, "_resize": 1, "_N$layoutType": 3, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 19.899999999999995, "_N$spacingY": 8, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "71CV9OX7VNVplWabo7M0/K"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 63}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": "a3LBztwF9OeparUqM65pgh"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 63}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_id": "a9lye61ttECK9cM14iSZaF"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 62}, "_enabled": true, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.75, "elastic": true, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 64}, "content": {"__id__": 64}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": null, "_id": "19nhgpNlhE6ZHLY+dHqEbN"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 62}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 5, "_left": 0, "_right": 0, "_top": 180.95900000000003, "_bottom": 129.041, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 576, "_id": "0eS4VvjK9Fz5E3fvKu5Mrd"}, {"__type__": "cc.Node", "_name": "bottom_overlay", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [{"__id__": 71}, {"__id__": 75}], "_active": true, "_components": [{"__id__": 84}, {"__id__": 85}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 94}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -311.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "9fYS2x5eJJq4CfwJyKFsTi"}, {"__type__": "cc.Node", "_name": "bg_bot_left", "_objFlags": 0, "_parent": {"__id__": 70}, "_children": [{"__id__": 72}], "_active": true, "_components": [{"__id__": 74}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 369.3333333, "height": 121.3333333}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-457.73299999999995, 13.945, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "2ceFsZeq5BRbadVWb7YWWH"}, {"__type__": "cc.Node", "_name": "bg_game_name", "_objFlags": 0, "_parent": {"__id__": 71}, "_children": [], "_active": true, "_components": [{"__id__": 73}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 269.3333333, "height": 116}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-37.094, 1.357, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "cf4yEjkYVBRKMcXMOlmSei"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 72}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "82de41c1-59e2-4e67-a3d2-35a3071ee2d3"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "f8d835f9-31d3-4e33-91ab-d8dca1fe6925"}, "_id": "82gg9R22dPprUMH58bWfuR"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 71}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 8, "_left": -2.399666649999972, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "63pobvjAhPPpnkJIdNPFZy"}, {"__type__": "cc.Node", "_name": "btnCreateTable", "_objFlags": 0, "_parent": {"__id__": 70}, "_children": [{"__id__": 76}, {"__id__": 78}], "_active": true, "_components": [{"__id__": 81}, {"__id__": 83}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 369.3333333, "height": 121.3333333}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [452.673, 6.353, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "278ehfrSNKTJZt1Y1al1RY"}, {"__type__": "cc.Node", "_name": "btn_create_table", "_objFlags": 0, "_parent": {"__id__": 75}, "_children": [], "_active": true, "_components": [{"__id__": 77}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 65.3333333, "height": 66}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-48.928, -7.806, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "3aMlbZiPtO+a8gg3Vv6fUO"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "7e94251c-123f-454e-ae75-6c0a25595cb2"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "f8d835f9-31d3-4e33-91ab-d8dca1fe6925"}, "_id": "e6a2/jyYpFDYuqpOMOGdTA"}, {"__type__": "cc.Node", "_name": "lbCreateTable", "_objFlags": 0, "_parent": {"__id__": 75}, "_children": [], "_active": true, "_components": [{"__id__": 79}, {"__id__": 80}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 177.92, "height": 31.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-7.716, -6.438, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "86iTn0QBBIKpnVUwuEs7wp"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "<PERSON><PERSON><PERSON> bàn ch<PERSON>i mới", "_N$string": "<PERSON><PERSON><PERSON> bàn ch<PERSON>i mới", "_fontSize": 25, "_lineHeight": 25, "_enableWrapText": false, "_N$file": {"__uuid__": "d2505d39-c061-4ba3-a77b-aa0e8e141d95"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "cePER2+WtH6YslqTvPxB5f"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "id": "txt_create_table", "isUpperCase": false, "_id": "64xuwToohGaKHG9BrCKpmn"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 82}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": "d0xKHzN39MRJL/0QeTukhz"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 6}, "component": "", "_componentId": "bef07HH7BlP/btnMHOsS2YX", "handler": "createRoom", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 32, "_left": 0, "_right": 2.6603333499999735, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "44qS9t12tNfLNDExoyZncO"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 70}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 44, "_left": 0, "_right": 0, "_top": 0, "_bottom": 1.5, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1280, "_originalHeight": 0, "_id": "f0O2KltZBBHJi7AXEFw3a/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 70}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6cac1460-03de-4186-b7e5-51b6fdf0422d"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "f8d835f9-31d3-4e33-91ab-d8dca1fe6925"}, "_id": "53qRiAPEREvrYpQPRlxuba"}, {"__type__": "cc.Node", "_name": "btnQuickPlay", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [{"__id__": 87}], "_active": true, "_components": [{"__id__": 90}, {"__id__": 91}, {"__id__": 93}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 235.5371901, "height": 85.1239669}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-138.832, -318.839, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f7udKOvrBEbZmKPOms7Iwo"}, {"__type__": "cc.Node", "_name": "lbQuickPlay", "_objFlags": 0, "_parent": {"__id__": 86}, "_children": [], "_active": true, "_components": [{"__id__": 88}, {"__id__": 89}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 203.13, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 9.555, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c5C2S6bPJDkaqBon6BE+ls"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "CHƠI NGAY", "_N$string": "CHƠI NGAY", "_fontSize": 30, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "cd5511c9-7bfa-4f43-81c6-7827c46d4941"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "f403+8iRNE4IETpAUV1s5V"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "id": "txt_play_now", "isUpperCase": false, "_id": "92QIeGWQdBFJ1iFORJucYV"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 86}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "252b357e-75f3-47b6-a12d-8b72b6337642"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "8e26ecca-82ca-45a6-9b08-e3704724db1c"}, "_id": "e6qbmY1TZEtKnN9zPrnu/5"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 86}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 92}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 1, "transition": 1, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": "53BywxrsBFqamflUZ/6Vyx"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 6}, "component": "", "_componentId": "bef07HH7BlP/btnMHOsS2YX", "handler": "playingNow", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 86}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": -1.4009834500000125, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "d2NH24aTxJdq0BMkyOqqtQ"}, {"__type__": "cc.Node", "_name": "btnRefesh", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [{"__id__": 95}], "_active": true, "_components": [{"__id__": 97}, {"__id__": 98}, {"__id__": 100}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 235.5371901, "height": 85.1239669}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [136.738, -319.957, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "53bSJ49jBDbp0aL8Tj+YpZ"}, {"__type__": "cc.Node", "_name": "lbRefresh", "_objFlags": 0, "_parent": {"__id__": 94}, "_children": [], "_active": true, "_components": [{"__id__": 96}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 159.25, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 9.252, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "74efTYfe5C0asECXDKTQAi"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 95}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "REFRESH", "_N$string": "REFRESH", "_fontSize": 30, "_lineHeight": 35, "_enableWrapText": false, "_N$file": {"__uuid__": "1801dba7-6e7f-4d92-b1d5-21e22b6d891e"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "9eT8K3s49N4YNRuIc9mCkz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 94}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "43f2ed81-2b39-462c-b091-0360965ef83c"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "8e26ecca-82ca-45a6-9b08-e3704724db1c"}, "_id": "a8lEj0qU5Pp4tuZF5EVIxL"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 94}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 99}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": "2atrgPuYRPZYWZvzMG/x0A"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 6}, "component": "", "_componentId": "bef07HH7BlP/btnMHOsS2YX", "handler": "refeshListRoom", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 94}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": -2.5189834500000075, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "00lVIOCARHyKBlDDu9rKBk"}, {"__type__": "cc.Node", "_name": "header", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [{"__id__": 102}, {"__id__": 109}], "_active": true, "_components": [{"__id__": 116}, {"__id__": 117}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 982, "height": 88.6666667}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 219.97800000000004, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "71zJrBW/dIopWTGTkSdcYJ"}, {"__type__": "cc.Node", "_name": "BtnSortMoney", "_objFlags": 0, "_parent": {"__id__": 101}, "_children": [{"__id__": 103}, {"__id__": 105}], "_active": true, "_components": [{"__id__": 107}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "38Beq0/VdH1LUqaJEQSnon"}, {"__type__": "cc.Node", "_name": "New Sprite", "_objFlags": 0, "_parent": {"__id__": 102}, "_children": [], "_active": true, "_components": [{"__id__": 104}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 21}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [123.482, -9.399, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "13nTj2X4hEBY9KIwgTKZw7"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 103}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "7525c03b-d587-4f83-b966-9e9e43ed0107"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "2ehKhB5KZOg7bAMN1qM5Eh"}, {"__type__": "cc.Node", "_name": "New Sprite", "_objFlags": 0, "_parent": {"__id__": 102}, "_children": [], "_active": true, "_components": [{"__id__": 106}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 21}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [123.482, 8.69, 0, 0, 0, 0, 1, 1, -1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "82iUsh8fxNO5Aomqor4BvK"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 105}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "7525c03b-d587-4f83-b966-9e9e43ed0107"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "b0G4XvV4pETJs7s3oHV0NF"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 102}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 108}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 1, "transition": 1, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": "30mSHgSS5IT6X6g2KkDsK7"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 6}, "component": "", "_componentId": "bef07HH7BlP/btnMHOsS2YX", "handler": "onBtnSortRoomMoney", "customEventData": ""}, {"__type__": "cc.Node", "_name": "BtnSortRoom", "_objFlags": 0, "_parent": {"__id__": 101}, "_children": [{"__id__": 110}, {"__id__": 112}], "_active": true, "_components": [{"__id__": 114}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-328.767, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "0bWBGlPdFE4ZZ9ggEBSkhQ"}, {"__type__": "cc.Node", "_name": "New Sprite", "_objFlags": 0, "_parent": {"__id__": 109}, "_children": [], "_active": true, "_components": [{"__id__": 111}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 21}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [107.953, -9.399, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d1HWN1CA1Hq69VsMiTlY/f"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 110}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "7525c03b-d587-4f83-b966-9e9e43ed0107"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "91ELbU0zJKYpAKm6mlaP6o"}, {"__type__": "cc.Node", "_name": "New Sprite", "_objFlags": 0, "_parent": {"__id__": 109}, "_children": [], "_active": true, "_components": [{"__id__": 113}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 21}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [107.953, 8.69, 0, 0, 0, 0, 1, 1, -1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "84Rc6H0TBNNrTn68rIUi2i"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 112}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "7525c03b-d587-4f83-b966-9e9e43ed0107"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "a9mTPnAO1I9pB1DeRtGrYo"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 109}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 115}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 1, "transition": 1, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": "351BvJa4pG4KqPVbbf0l0O"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 6}, "component": "", "_componentId": "bef07HH7BlP/btnMHOsS2YX", "handler": "onBtnSortRoomId", "customEventData": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 101}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "63c53af6-75f1-487a-a147-998daeaf342e"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "f8d835f9-31d3-4e33-91ab-d8dca1fe6925"}, "_id": "8dxW7YbRBCCpkLHdOzUDFd"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 101}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": 95.68866664999996, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "05lJIGeAhA86yX9QB0Iisu"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "c26qjI54pDhbcstDv746J9"}, {"__type__": "bef07HH7BlP/btnMHOsS2YX", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "itemListTemp": {"__uuid__": "0292820d-7c3c-4262-a931-09a0b087a8fa"}, "contentListRooms": {"__id__": 64}, "labelNickName": {"__id__": 53}, "labelCoin": {"__id__": 58}, "scrRoom": {"__id__": 68}, "UI_Playing": null, "uiInGamePr": null, "btnHideRoomFull": {"__id__": 19}, "edtFindRoom": {"__id__": 38}, "soundManager": {"__id__": 120}, "_id": "8aoVD6ewBBnIjMvmbXIpi4"}, {"__type__": "BaCay.Room.SoundManager", "bgMusic": {"__id__": 121}, "effSound": {"__id__": 125}, "listAudio": [{"__uuid__": "18096e24-13f9-40bd-a689-c81c54ac2c6b"}, {"__uuid__": "39fd09c1-09ac-41d3-8425-9290edb91573"}, {"__uuid__": "01f08d83-4212-4586-900b-fa3423ebf0ac"}, {"__uuid__": "d24644dc-**************-260dc3b53b1d"}, {"__uuid__": "6b1bf844-3e7b-4088-843b-1722d347281f"}, {"__uuid__": "d944f5b9-4956-43da-9a4b-8dbed1177885"}, {"__uuid__": "02fe152d-3bbf-44b9-92ea-f7a822afbc65"}]}, {"__type__": "cc.AudioSource", "_name": "", "_objFlags": 0, "node": {"__id__": 122}, "_enabled": true, "_clip": null, "_volume": 0.5, "_mute": false, "_loop": true, "_firstlyEnabled": true, "playOnLoad": false, "preload": false, "_id": "6b60jUn7xJ9ZClHuunrsCK"}, {"__type__": "cc.Node", "_name": "MusicSource", "_objFlags": 0, "_parent": {"__id__": 123}, "_children": [], "_active": true, "_components": [{"__id__": 121}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e8Kz+KVfxFObhs1Y+ZHj1V"}, {"__type__": "cc.Node", "_name": "SoundManager", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 122}, {"__id__": 124}], "_active": true, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [640, 360, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "ae3K0bA1hAsab5J82wgUdX"}, {"__type__": "cc.Node", "_name": "EffectSource", "_objFlags": 0, "_parent": {"__id__": 123}, "_children": [], "_active": true, "_components": [{"__id__": 125}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "9cWd5q2l5KhoXYWhEksl/g"}, {"__type__": "cc.AudioSource", "_name": "", "_objFlags": 0, "node": {"__id__": 124}, "_enabled": true, "_clip": null, "_volume": 1, "_mute": false, "_loop": false, "_firstlyEnabled": true, "playOnLoad": false, "preload": false, "_id": "abA4LWYnNJFLoAVFl/pxkC"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1280, "_originalHeight": 720, "_id": "27gyjK8HxJXaASrNbAulPj"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_designResolution": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_fitWidth": true, "_fitHeight": true, "_id": "87X1ZcKh1Es4Uq8l97e2LQ"}, {"__type__": "2a33dcxF+NBW6IzkrkZ/0RB", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "designResolution": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_id": "547svnfRFEqZXe7k4MYU5v"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "01NWxtH9tGo6HFkQVOYMpl"}, {"__type__": "8d9fcI6tENBhbjEpxnzpxlp", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_id": "755kbnprBGBpk2CdXoaZoz"}]
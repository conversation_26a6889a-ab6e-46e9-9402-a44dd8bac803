<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>frames</key>
		<dict>
			<key>100k.png</key>
			<dict>
				<key>frame</key>
				<string>{{764,829},{83,84}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{83,84}}</string>
				<key>sourceSize</key>
				<string>{83,84}</string>
			</dict>

			<key>10k.png</key>
			<dict>
				<key>frame</key>
				<string>{{791,554},{83,84}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{83,84}}</string>
				<key>sourceSize</key>
				<string>{83,84}</string>
			</dict>

			<key>1k.png</key>
			<dict>
				<key>frame</key>
				<string>{{764,914},{83,84}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{83,84}}</string>
				<key>sourceSize</key>
				<string>{83,84}</string>
			</dict>

			<key>3702fdfdb5c11a2fc6eecf035e82d428ccbe1d9a180e6-6usF7v.png</key>
			<dict>
				<key>frame</key>
				<string>{{130,962},{51,51}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{51,51}}</string>
				<key>sourceSize</key>
				<string>{51,51}</string>
			</dict>

			<key>500k.png</key>
			<dict>
				<key>frame</key>
				<string>{{764,744},{83,84}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{83,84}}</string>
				<key>sourceSize</key>
				<string>{83,84}</string>
			</dict>

			<key>50k.png</key>
			<dict>
				<key>frame</key>
				<string>{{848,744},{83,84}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{83,84}}</string>
				<key>sourceSize</key>
				<string>{83,84}</string>
			</dict>

			<key>Group 25.png</key>
			<dict>
				<key>frame</key>
				<string>{{272,755},{233,99}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{233,99}}</string>
				<key>sourceSize</key>
				<string>{233,99}</string>
			</dict>

			<key>Group 26.png</key>
			<dict>
				<key>frame</key>
				<string>{{0,595},{271,366}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{271,366}}</string>
				<key>sourceSize</key>
				<string>{271,366}</string>
			</dict>

			<key>Shape 6 copy 2.png</key>
			<dict>
				<key>frame</key>
				<string>{{535,554},{127,94}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{127,94}}</string>
				<key>sourceSize</key>
				<string>{127,94}</string>
			</dict>

			<key>Shape 6 copy 3.png</key>
			<dict>
				<key>frame</key>
				<string>{{272,855},{127,94}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{127,94}}</string>
				<key>sourceSize</key>
				<string>{127,94}</string>
			</dict>

			<key>Shape 6 copy.png</key>
			<dict>
				<key>frame</key>
				<string>{{764,649},{127,94}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{127,94}}</string>
				<key>sourceSize</key>
				<string>{127,94}</string>
			</dict>

			<key>Shape 6.png</key>
			<dict>
				<key>frame</key>
				<string>{{663,554},{127,94}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{127,94}}</string>
				<key>sourceSize</key>
				<string>{127,94}</string>
			</dict>

			<key>atFillter.png</key>
			<dict>
				<key>frame</key>
				<string>{{0,962},{129,54}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{129,54}}</string>
				<key>sourceSize</key>
				<string>{129,54}</string>
			</dict>

			<key>bgHistory.png</key>
			<dict>
				<key>frame</key>
				<string>{{0,554},{530,40}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{530,40}}</string>
				<key>sourceSize</key>
				<string>{530,40}</string>
			</dict>

			<key>bg_current.png</key>
			<dict>
				<key>frame</key>
				<string>{{272,668},{250,86}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{250,86}}</string>
				<key>sourceSize</key>
				<string>{250,86}</string>
			</dict>

			<key>bg_jackpot.png</key>
			<dict>
				<key>frame</key>
				<string>{{272,595},{262,72}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{262,72}}</string>
				<key>sourceSize</key>
				<string>{262,72}</string>
			</dict>

			<key>close.png</key>
			<dict>
				<key>frame</key>
				<string>{{400,855},{92,95}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{92,95}}</string>
				<key>sourceSize</key>
				<string>{92,95}</string>
			</dict>

			<key>ic_guide.png</key>
			<dict>
				<key>frame</key>
				<string>{{848,903},{70,73}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{70,73}}</string>
				<key>sourceSize</key>
				<string>{70,73}</string>
			</dict>

			<key>ic_history.png</key>
			<dict>
				<key>frame</key>
				<string>{{272,950},{70,73}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{70,73}}</string>
				<key>sourceSize</key>
				<string>{70,73}</string>
			</dict>

			<key>ic_rank.png</key>
			<dict>
				<key>frame</key>
				<string>{{848,829},{70,73}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{70,73}}</string>
				<key>sourceSize</key>
				<string>{70,73}</string>
			</dict>

			<key>khung-bg.png</key>
			<dict>
				<key>frame</key>
				<string>{{0,0},{1033,553}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{1033,553}}</string>
				<key>sourceSize</key>
				<string>{1033,553}</string>
			</dict>

			<key>upp.png</key>
			<dict>
				<key>frame</key>
				<string>{{523,668},{240,322}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{240,322}}</string>
				<key>sourceSize</key>
				<string>{240,322}</string>
			</dict>

		</dict>

		<key>metadata</key>
		<dict>
			<key>format</key>
			<integer>2</integer>
			<key>size</key>
			<string>{2048,1024}</string>
			<key>textureFileName</key>
			<string>caothap.png</string>
			</dict>
		</dict>
</plist>
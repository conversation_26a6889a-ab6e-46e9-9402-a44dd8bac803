[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "PopupGuide", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 5}], "_active": true, "_components": [{"__id__": 42}, {"__id__": 43}, {"__id__": 44}], "_prefab": {"__id__": 45}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [640, 360, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "Bg", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}], "_prefab": {"__id__": 4}, "_opacity": 128, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a0e7be15-6b58-46f9-9f0d-6ee5f6170b16"}, "fileId": "8ehbO/NG5J/64qVcBdmSWZ", "sync": false}, {"__type__": "cc.Node", "_name": "Container", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 6}, {"__id__": 11}, {"__id__": 14}], "_active": true, "_components": [{"__id__": 40}], "_prefab": {"__id__": 41}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 879, "height": 500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -1, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "BtnClose", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [], "_active": true, "_components": [{"__id__": 7}, {"__id__": 8}], "_prefab": {"__id__": 10}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 104.1666667, "height": 54.8611111}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [385.265, 223.046, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "4c35a713-ef56-425f-b6a5-9dee803f40c0"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": {"__uuid__": "8e26ecca-82ca-45a6-9b08-e3704724db1c"}, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 9}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 6}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "89938zvBc1CAaNyePwL3b4R", "handler": "dismiss", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a0e7be15-6b58-46f9-9f0d-6ee5f6170b16"}, "fileId": "e85FTC5GxDBoLCQ4ho7GP6", "sync": false}, {"__type__": "cc.Node", "_name": "lbTitle", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [], "_active": true, "_components": [{"__id__": 12}], "_prefab": {"__id__": 13}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 178.06, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-9.106, 212.286, 0, 0, 0, 0.0026179908874179934, 0.9999965730559848, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0.3}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "HƯỚNG DẪN", "_N$string": "HƯỚNG DẪN", "_fontSize": 25, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "cd5511c9-7bfa-4f43-81c6-7827c46d4941"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a0e7be15-6b58-46f9-9f0d-6ee5f6170b16"}, "fileId": "29qsjeMSREKr3EwSeUOpqO", "sync": false}, {"__type__": "cc.Node", "_name": "New ScrollView", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [{"__id__": 15}], "_active": true, "_components": [{"__id__": 38}], "_prefab": {"__id__": 39}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 810, "height": 364}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -9.236, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [{"__id__": 16}], "_active": true, "_components": [{"__id__": 36}], "_prefab": {"__id__": 37}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 830, "height": 364}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 15}, "_children": [{"__id__": 17}, {"__id__": 20}, {"__id__": 23}, {"__id__": 26}, {"__id__": 29}, {"__id__": 32}], "_active": true, "_components": [], "_prefab": {"__id__": 35}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 780, "height": 760}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 182, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "1", "_objFlags": 0, "_parent": {"__id__": 16}, "_children": [], "_active": true, "_components": [{"__id__": 18}], "_prefab": {"__id__": 19}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 91.04, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-390.098, -30, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 17}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "GIỚI THIỆU", "_N$string": "GIỚI THIỆU", "_fontSize": 20, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d2505d39-c061-4ba3-a77b-aa0e8e141d95"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a0e7be15-6b58-46f9-9f0d-6ee5f6170b16"}, "fileId": "825mrCZzBOvIV75oXsM+3A", "sync": false}, {"__type__": "cc.Node", "_name": "1_1", "_objFlags": 0, "_parent": {"__id__": 16}, "_children": [], "_active": true, "_components": [{"__id__": 21}], "_prefab": {"__id__": 22}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 682.04, "height": 104.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-390.098, -100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 20}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "<PERSON> Thấp là một game đơn giản với tỷ lệ thắng lớn, c<PERSON> hội sở hữu quỹ thưởng khủng sẽ mang lại \ncho người chơi những trải nghiệm cực kỳ thú vị. Giao diện game sẽ xuất hiện 1 trong 52 lá bài, \nngười chới sẽ dự đoán lá bài tiếp theo sẽ lớn hơn hay nhỏ hơn lá bài hiện tại .", "_N$string": "<PERSON> Thấp là một game đơn giản với tỷ lệ thắng lớn, c<PERSON> hội sở hữu quỹ thưởng khủng sẽ mang lại \ncho người chơi những trải nghiệm cực kỳ thú vị. Giao diện game sẽ xuất hiện 1 trong 52 lá bài, \nngười chới sẽ dự đoán lá bài tiếp theo sẽ lớn hơn hay nhỏ hơn lá bài hiện tại .", "_fontSize": 17, "_lineHeight": 32, "_enableWrapText": true, "_N$file": {"__uuid__": "d2505d39-c061-4ba3-a77b-aa0e8e141d95"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a0e7be15-6b58-46f9-9f0d-6ee5f6170b16"}, "fileId": "0dSJ+rUgJNrLUypop3jelu", "sync": false}, {"__type__": "cc.Node", "_name": "2", "_objFlags": 0, "_parent": {"__id__": 16}, "_children": [], "_active": true, "_components": [{"__id__": 24}], "_prefab": {"__id__": 25}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 89.06, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-390.098, -180, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "LUẬT CHƠI", "_N$string": "LUẬT CHƠI", "_fontSize": 20, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d2505d39-c061-4ba3-a77b-aa0e8e141d95"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a0e7be15-6b58-46f9-9f0d-6ee5f6170b16"}, "fileId": "d9W+MZBgFIYqZjNCuulFcm", "sync": false}, {"__type__": "cc.Node", "_name": "2_1", "_objFlags": 0, "_parent": {"__id__": 16}, "_children": [], "_active": true, "_components": [{"__id__": 27}], "_prefab": {"__id__": 28}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 665.38, "height": 392.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-390.098, -390, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Bước 1 : <PERSON><PERSON><PERSON> mứ<PERSON> cược\nBước 2 : <PERSON><PERSON><PERSON> \"PLAY\" để tham gia chơi ngay.\n               <PERSON><PERSON> thống sẽ ngẫu nhiên xuất hiện 1 lá bài để người chơi đưa ra lựa chọn của mình\nBước 3 : Dự đoán lá bài tiếp theo lớn hơn hay nhỏ hơn lá bài đã xuất hiện\n               Mỗi dự đoán sẽ có tỷ lệ trúng thưởng khác nhau.\n               Mỗi lượt dự đoán có thời gian tối đa là 2 phút. Hết 2 phút người chơi không đưa ra \n               dự đoán tiếp theo thì hệ thống sẽ tự động chuyển sang lượt mới.  Trường hợp đặc biệt \n               nếu người chơi sau khi ấn \"PLAY\" mà không dự đoán kết quả sau 2 phút thì \n               hệ thống sẽ lựa chọn ngẫu nhiên 1 đáp án cho người chơi.\nBước 4 : <PERSON> dõi kết quả để biết kết quả thắng và thua\n               Trường hợp xuất hiện lá bài tiếp theo cùng giá trị với lá bài hiện tại, hệ thống sẽ trích \n                10% số tiền đặt của người chơi vào Jackpot và lượt chơi tiếp tục.", "_N$string": "Bước 1 : <PERSON><PERSON><PERSON> mứ<PERSON> cược\nBước 2 : <PERSON><PERSON><PERSON> \"PLAY\" để tham gia chơi ngay.\n               <PERSON><PERSON> thống sẽ ngẫu nhiên xuất hiện 1 lá bài để người chơi đưa ra lựa chọn của mình\nBước 3 : Dự đoán lá bài tiếp theo lớn hơn hay nhỏ hơn lá bài đã xuất hiện\n               Mỗi dự đoán sẽ có tỷ lệ trúng thưởng khác nhau.\n               Mỗi lượt dự đoán có thời gian tối đa là 2 phút. Hết 2 phút người chơi không đưa ra \n               dự đoán tiếp theo thì hệ thống sẽ tự động chuyển sang lượt mới.  Trường hợp đặc biệt \n               nếu người chơi sau khi ấn \"PLAY\" mà không dự đoán kết quả sau 2 phút thì \n               hệ thống sẽ lựa chọn ngẫu nhiên 1 đáp án cho người chơi.\nBước 4 : <PERSON> dõi kết quả để biết kết quả thắng và thua\n               Trường hợp xuất hiện lá bài tiếp theo cùng giá trị với lá bài hiện tại, hệ thống sẽ trích \n                10% số tiền đặt của người chơi vào Jackpot và lượt chơi tiếp tục.", "_fontSize": 17, "_lineHeight": 32, "_enableWrapText": true, "_N$file": {"__uuid__": "d2505d39-c061-4ba3-a77b-aa0e8e141d95"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a0e7be15-6b58-46f9-9f0d-6ee5f6170b16"}, "fileId": "2dVPPr8s5AAIjuXrXrvOlx", "sync": false}, {"__type__": "cc.Node", "_name": "3", "_objFlags": 0, "_parent": {"__id__": 16}, "_children": [], "_active": true, "_components": [{"__id__": 30}], "_prefab": {"__id__": 31}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 217.26, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-390.098, -615, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "CƠ CHẾ NỔ QUỸ THƯỞNG", "_N$string": "CƠ CHẾ NỔ QUỸ THƯỞNG", "_fontSize": 20, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d2505d39-c061-4ba3-a77b-aa0e8e141d95"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a0e7be15-6b58-46f9-9f0d-6ee5f6170b16"}, "fileId": "0aTTfocZNFxowV5qftXmhc", "sync": false}, {"__type__": "cc.Node", "_name": "3_1", "_objFlags": 0, "_parent": {"__id__": 16}, "_children": [], "_active": true, "_components": [{"__id__": 33}], "_prefab": {"__id__": 34}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 665.31, "height": 104.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-390.098, -680, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 32}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "<PERSON>hi người chơi thắng liên tiếp trong cùng một phiên chơi và sở hữu bất kỳ 3 trong 4 quân A thì\nquỹ thưởng sẽ nổ. <PERSON><PERSON> đ<PERSON>, người chơi sẽ nhận được 50% quỹ thưởng.\nTham gia ngay CAO THẤP để rinh hàng triệu $ nào.", "_N$string": "<PERSON>hi người chơi thắng liên tiếp trong cùng một phiên chơi và sở hữu bất kỳ 3 trong 4 quân A thì\nquỹ thưởng sẽ nổ. <PERSON><PERSON> đ<PERSON>, người chơi sẽ nhận được 50% quỹ thưởng.\nTham gia ngay CAO THẤP để rinh hàng triệu $ nào.", "_fontSize": 17, "_lineHeight": 32, "_enableWrapText": true, "_N$file": {"__uuid__": "d2505d39-c061-4ba3-a77b-aa0e8e141d95"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a0e7be15-6b58-46f9-9f0d-6ee5f6170b16"}, "fileId": "1biQT1nHtBj7/PmWY5E+Qi", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a0e7be15-6b58-46f9-9f0d-6ee5f6170b16"}, "fileId": "8d14yheTZG65VKBdgU7LUg", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a0e7be15-6b58-46f9-9f0d-6ee5f6170b16"}, "fileId": "79LH6S+gNHg7ooVDi6brOt", "sync": false}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.75, "elastic": true, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 16}, "content": {"__id__": 16}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a0e7be15-6b58-46f9-9f0d-6ee5f6170b16"}, "fileId": "e0YSJtLl1ChIFaofI7+/xB", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "278af286-cdc8-4e74-8ee7-5a9e4532a952"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a0e7be15-6b58-46f9-9f0d-6ee5f6170b16"}, "fileId": "2ffvI60htC55tT65L9RezY", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1280, "_originalHeight": 720, "_id": ""}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_id": ""}, {"__type__": "89938zvBc1CAaNyePwL3b4R", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "bg": null, "container": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a0e7be15-6b58-46f9-9f0d-6ee5f6170b16"}, "fileId": "", "sync": false}]
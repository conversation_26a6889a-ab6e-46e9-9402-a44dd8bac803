<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>frames</key>
    <dict>
      <key>1.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,0},{157,158}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{157,158}}</string>
        <key>sourceSize</key>
        <string>{157,158}</string>
      </dict>
      <key>10.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,158},{157,158}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{157,158}}</string>
        <key>sourceSize</key>
        <string>{157,158}</string>
      </dict>
      <key>2.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,316},{157,158}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{157,158}}</string>
        <key>sourceSize</key>
        <string>{157,158}</string>
      </dict>
      <key>3.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,474},{157,158}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{157,158}}</string>
        <key>sourceSize</key>
        <string>{157,158}</string>
      </dict>
      <key>4.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,632},{157,158}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{157,158}}</string>
        <key>sourceSize</key>
        <string>{157,158}</string>
      </dict>
      <key>5.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,790},{157,158}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{157,158}}</string>
        <key>sourceSize</key>
        <string>{157,158}</string>
      </dict>
      <key>6.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,948},{157,158}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{157,158}}</string>
        <key>sourceSize</key>
        <string>{157,158}</string>
      </dict>
      <key>7.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,1106},{157,158}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{157,158}}</string>
        <key>sourceSize</key>
        <string>{157,158}</string>
      </dict>
      <key>8.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,1264},{157,158}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{157,158}}</string>
        <key>sourceSize</key>
        <string>{157,158}</string>
      </dict>
      <key>9.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,1422},{157,158}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{157,158}}</string>
        <key>sourceSize</key>
        <string>{157,158}</string>
      </dict>
    </dict>
    <key>metadata</key>
    <dict>
      <key>format</key>
      <integer>2</integer>
      <key>pixelFormat</key>
      <string>RGBA8888</string>
      <key>premultiplyAlpha</key>
      <false/>
      <key>realTextureFileName</key>
      <string>popupChangeAvatar.png</string>
      <key>size</key>
      <string>{157,1580}</string>
      <key>textureFileName</key>
      <string>popupChangeAvatar</string>
    </dict>
  </dict>
</plist>

{"skeleton": {"hash": "CIka4GM0tx1rt7tAuBmNwgH4zc0", "spine": "3.7.93", "width": 124, "height": 124, "images": "", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 6.34, "rotation": 90, "x": -93.5, "y": 38.99}, {"name": "bone2", "parent": "root", "length": 6.71, "x": 130.14, "y": 0.03}, {"name": "bone3", "parent": "root", "length": 12.99, "rotation": 90, "x": -11.9, "y": -20.72}, {"name": "bone4", "parent": "bone3", "x": 20.1, "y": 79.37}, {"name": "bone5", "parent": "bone3", "x": 19.49, "y": 47.1}, {"name": "bone6", "parent": "bone3", "x": 20.5, "y": 17.25}, {"name": "bone7", "parent": "bone3", "x": 20.3, "y": -14.41}, {"name": "bone8", "parent": "bone3", "x": 19.69, "y": -34.51}, {"name": "bone9", "parent": "bone3", "x": 19.69, "y": -54.4}, {"name": "bone10", "parent": "bone3", "x": 19.69, "y": -82.01}, {"name": "bone11", "parent": "root", "length": 6.71, "x": 144.35, "y": 0.03}, {"name": "bone12", "parent": "root", "length": 6.71, "x": 157.14, "y": 0.03}, {"name": "bone13", "parent": "root", "length": 6.34, "rotation": 90, "x": -65.3, "y": 38.99}, {"name": "bone14", "parent": "root", "length": 6.34, "rotation": 90, "x": -37.44, "y": 38.99}, {"name": "bone15", "parent": "root", "length": 6.34, "rotation": 90, "x": -9.87, "y": 38.99}, {"name": "bone16", "parent": "root", "length": 6.34, "rotation": 90, "x": 19.63, "y": 38.99}, {"name": "bone17", "parent": "root", "length": 6.34, "rotation": 90, "x": 47.74, "y": 38.99}, {"name": "bone18", "parent": "root", "length": 6.34, "rotation": 90, "x": 74.94, "y": 38.99}, {"name": "bone19", "parent": "root", "length": 6.34, "rotation": 90, "x": 102, "y": 27.24}, {"name": "bone20", "parent": "root", "length": 6.34, "rotation": 90, "x": 114.01, "y": -2.33}, {"name": "bone21", "parent": "root", "length": 6.34, "rotation": 90, "x": 101.73, "y": -28.18}, {"name": "bone22", "parent": "root", "length": 6.34, "rotation": 90, "x": 74.39, "y": -38.36}, {"name": "bone23", "parent": "root", "length": 6.34, "rotation": 90, "x": 47.05, "y": -38.36}, {"name": "bone24", "parent": "root", "length": 6.34, "rotation": 90, "x": 19.71, "y": -38.36}, {"name": "bone25", "parent": "root", "length": 6.34, "rotation": 90, "x": -9.5, "y": -38.36}, {"name": "bone26", "parent": "root", "length": 6.34, "rotation": 90, "x": -38.14, "y": -38.36}, {"name": "bone27", "parent": "root", "length": 6.34, "rotation": 90, "x": -65.85, "y": -38.36}, {"name": "bone28", "parent": "root", "length": 6.34, "rotation": 90, "x": -94.13, "y": -38.36}, {"name": "bone29", "parent": "root", "length": 6.34, "rotation": 90, "x": -121.84, "y": -38.36}, {"name": "bone30", "parent": "root", "length": 6.34, "rotation": 90, "x": -144.16, "y": -25.2}, {"name": "bone31", "parent": "root", "length": 59.16, "rotation": 6.5, "x": -189.76, "y": 0.75}, {"name": "bone32", "parent": "root", "x": -154.97, "y": -23.06}, {"name": "bone33", "parent": "bone31", "length": 18.05, "rotation": 84.68, "x": 16.08, "y": 36.63}, {"name": "bone34", "parent": "bone31", "length": 18.05, "rotation": 84.68, "x": 44.23, "y": 41.9, "scaleX": 0.867, "scaleY": 0.867}, {"name": "bone35", "parent": "bone33", "length": 10.02, "rotation": -0.27, "x": 20, "y": 3.52}, {"name": "bone36", "parent": "bone33", "length": 10.02, "rotation": -0.27, "x": 22.9, "y": -36.52}, {"name": "bone37", "parent": "root", "length": 27.18, "rotation": -15.13, "x": -42.4, "y": 21.09}, {"name": "bone38", "parent": "root", "length": 22.77, "rotation": 75.51, "x": -21.97, "y": -10.05, "scaleX": 0.882, "scaleY": 0.882}, {"name": "bone39", "parent": "root", "length": 11.75, "rotation": 90, "x": 26.31, "y": 3.04}, {"name": "bone40", "parent": "root", "length": 22.77, "rotation": 75.51, "x": -21.97, "y": -10.05, "scaleX": 0.882, "scaleY": 0.882}, {"name": "bone41", "parent": "root", "length": 22.77, "rotation": 75.51, "x": -21.97, "y": -10.05, "scaleX": 0.882, "scaleY": 0.882}, {"name": "bone42", "parent": "root", "length": 7.22, "rotation": 90, "x": -45.06, "y": -28.6}, {"name": "bone43", "parent": "root", "length": 4.39, "rotation": 90, "x": 0.04, "y": -32}, {"name": "bone44", "parent": "bone43", "x": 18.84, "y": 42.98, "scaleX": 0.509}, {"name": "bone45", "parent": "bone43", "x": 18.84, "y": 30.1, "scaleX": 0.449}, {"name": "bone46", "parent": "bone43", "x": 18.75, "y": 14.88, "scaleX": 0.428}, {"name": "bone47", "parent": "bone43", "x": 18.67, "y": 0.91, "scaleX": 0.402}, {"name": "bone48", "parent": "bone43", "x": 18.84, "y": -13.62, "scaleX": 0.411}, {"name": "bone49", "parent": "bone43", "x": 18.75, "y": -28.84, "scaleX": 0.399}, {"name": "bone50", "parent": "bone43", "x": 18.5, "y": -43.17, "scaleX": 0.397}, {"name": "bone51", "parent": "root", "length": 7.22, "rotation": 90, "x": -29.94, "y": -28.6}, {"name": "bone52", "parent": "root", "length": 7.22, "rotation": 90, "x": -14.99, "y": -28.6}, {"name": "bone53", "parent": "root", "length": 7.22, "rotation": 90, "x": -0.7, "y": -28.6}, {"name": "bone54", "parent": "root", "length": 7.22, "rotation": 90, "x": 13.89, "y": -28.6}, {"name": "bone55", "parent": "root", "length": 7.22, "rotation": 90, "x": 29.06, "y": -28.6}, {"name": "bone56", "parent": "root", "length": 7.22, "rotation": 90, "x": 43.27, "y": -28.6}, {"name": "bone57", "parent": "root", "length": 22.69, "rotation": 90, "x": 0.9, "y": -2.92, "scaleX": 0.717, "scaleY": 0.717}, {"name": "bone58", "parent": "root", "length": 20.34, "rotation": 90.23, "x": -0.56, "y": -14, "scaleX": 0.255, "scaleY": 0.255}, {"name": "bone59", "parent": "root", "length": 20.34, "rotation": 90.23, "x": -7.42, "y": -14.03, "scaleX": 0.255, "scaleY": 0.255}, {"name": "bone60", "parent": "root", "length": 20.34, "rotation": 90.23, "x": 5.93, "y": -13.98, "scaleX": 0.255, "scaleY": 0.255}, {"name": "bone61", "parent": "root", "length": 11.41, "rotation": 107.6, "x": -21.21, "y": -12.45}, {"name": "bone62", "parent": "root", "length": 49.13, "rotation": 90}, {"name": "bone63", "parent": "root", "length": 86.33, "x": -628.08, "y": 55.01}, {"name": "bone64", "parent": "root", "length": 3.55, "rotation": 180, "x": 53.32, "y": 87.77}, {"name": "bone65", "parent": "bone64", "length": 4.23, "rotation": -1.27, "x": 3.6, "y": 0.12}, {"name": "bone66", "parent": "bone65", "length": 4.47, "rotation": 0.67, "x": 4.23, "y": 0.05}, {"name": "bone67", "parent": "bone66", "length": 4.61, "rotation": -0.57, "x": 4.61}, {"name": "bone68", "parent": "bone67", "length": 5.26, "rotation": 0.15, "x": 4.65}, {"name": "bone69", "parent": "bone68", "length": 5.64, "rotation": 1.5, "x": 5.26}, {"name": "bone70", "parent": "bone69", "length": 6.2, "rotation": -0.91, "x": 5.64}, {"name": "bone71", "parent": "bone70", "length": 6.02, "rotation": 0.88, "x": 6.2}, {"name": "bone72", "parent": "bone71", "length": 7.1, "rotation": -0.07, "x": 6.01, "y": 0.05}, {"name": "bone73", "parent": "bone72", "length": 6.06, "rotation": -0.82, "x": 7.1}, {"name": "bone74", "parent": "bone73", "length": 7.38, "rotation": -0.29, "x": 6.06}, {"name": "bone75", "parent": "bone74", "length": 7.47, "rotation": 1.45, "x": 7.38}, {"name": "bone76", "parent": "bone75", "length": 6.77, "rotation": -1.52, "x": 7.48, "y": 0.09}, {"name": "bone77", "parent": "bone76", "length": 6.58, "rotation": 0.39, "x": 6.77}, {"name": "bone78", "parent": "bone77", "length": 5.88, "rotation": -0.51, "x": 6.58}, {"name": "bone79", "parent": "bone78", "length": 7.57, "rotation": 0.56, "x": 5.88}, {"name": "bone80", "parent": "bone79", "length": 6.72, "rotation": 0.36, "x": 7.57}, {"name": "bone81", "parent": "bone80", "length": 8.37, "x": 6.72}, {"name": "bone82", "parent": "bone81", "length": 7.52, "rotation": -1.07, "x": 8.37}, {"name": "bone83", "parent": "bone82", "length": 7.76, "rotation": 0.73, "x": 7.8, "y": 0.05}, {"name": "bone84", "parent": "bone83", "length": 7.05, "rotation": 0.35, "x": 7.76}, {"name": "bone85", "parent": "bone84", "length": 7.1, "rotation": -0.38, "x": 7.05}, {"name": "bone86", "parent": "bone85", "length": 6.72, "rotation": 0.38, "x": 7.14}, {"name": "bone87", "parent": "bone86", "length": 5.59, "rotation": -0.48, "x": 6.72}, {"name": "bone88", "parent": "root"}, {"name": "bone89", "parent": "root", "length": 38.78, "rotation": 90, "x": 0.51, "y": -19.02}, {"name": "bone90", "parent": "root", "length": 38.78, "rotation": 90, "x": 0.51, "y": -19.02}, {"name": "bone91", "parent": "root", "length": 38.78, "rotation": 90, "x": 0.51, "y": -19.02}, {"name": "bone92", "parent": "root", "length": 17.95, "rotation": 90, "scaleX": 0.604, "scaleY": 0.604}, {"name": "bone93", "parent": "root", "length": 17.95, "rotation": 90, "scaleX": 0.596, "scaleY": 0.596}, {"name": "bone94", "parent": "root", "length": 6.31, "rotation": 179.26, "x": -10.71, "y": -86.41}, {"name": "bone95", "parent": "bone94", "length": 5.49, "rotation": -0.11, "x": 6.31}, {"name": "bone96", "parent": "bone95", "length": 4.84, "rotation": 0.86, "x": 5.49}, {"name": "bone97", "parent": "bone96", "length": 5.08, "rotation": 0.92, "x": 4.84}, {"name": "bone98", "parent": "bone97", "length": 5.66, "rotation": -0.92, "x": 5.08}, {"name": "bone99", "parent": "bone98", "length": 5.66, "x": 5.66}, {"name": "bone100", "parent": "bone99", "length": 6.07, "rotation": -0.77, "x": 5.66}, {"name": "bone101", "parent": "bone100", "length": 5.66, "rotation": 0.77, "x": 6.07}, {"name": "bone102", "parent": "bone101", "length": 5.58, "x": 5.66}, {"name": "bone103", "parent": "bone102", "length": 6.4, "x": 5.58}, {"name": "bone104", "parent": "bone103", "length": 6.64, "rotation": -0.71, "x": 6.4}, {"name": "bone105", "parent": "bone104", "length": 6.81, "rotation": 0.02, "x": 6.64}, {"name": "bone106", "parent": "bone105", "length": 6.64, "rotation": 0.69, "x": 6.81}, {"name": "bone107", "parent": "bone106", "length": 6.56, "x": 6.64}, {"name": "bone108", "parent": "bone107", "length": 5.9, "x": 6.56}, {"name": "bone109", "parent": "bone108", "length": 5.76, "x": 6.61, "y": -0.04}, {"name": "bone110", "parent": "bone109", "length": 6.32, "rotation": -0.73, "x": 5.84, "y": -0.08}, {"name": "bone111", "parent": "bone110", "length": 5.68, "rotation": 0.73, "x": 6.32}, {"name": "bone112", "parent": "bone111", "length": 5.52, "rotation": -0.83, "x": 5.68}, {"name": "bone113", "parent": "bone112", "length": 6, "rotation": 0.83, "x": 5.52}, {"name": "bone114", "parent": "bone113", "length": 6.32, "x": 6}, {"name": "bone115", "parent": "bone114", "length": 5.76, "x": 6.32}, {"name": "bone116", "parent": "bone115", "length": 5.2, "rotation": -0.88, "x": 5.76}, {"name": "bone117", "parent": "bone116", "length": 5.68, "rotation": -0.73, "x": 5.2}, {"name": "bone118", "parent": "root", "length": 6.31, "rotation": 179.26, "x": -11.28, "y": -130.47}, {"name": "bone119", "parent": "bone118", "length": 5.49, "rotation": -0.11, "x": 6.31}, {"name": "bone120", "parent": "bone119", "length": 4.84, "rotation": 0.86, "x": 5.49}, {"name": "bone121", "parent": "bone120", "length": 5.08, "rotation": 0.92, "x": 4.84}, {"name": "bone122", "parent": "bone121", "length": 5.66, "rotation": -0.92, "x": 5.08}, {"name": "bone123", "parent": "bone122", "length": 5.66, "x": 5.66}, {"name": "bone124", "parent": "bone123", "length": 6.07, "rotation": -0.77, "x": 5.66}, {"name": "bone125", "parent": "bone124", "length": 5.66, "rotation": 0.77, "x": 6.07}, {"name": "bone126", "parent": "bone125", "length": 5.58, "x": 5.66}, {"name": "bone127", "parent": "bone126", "length": 6.4, "x": 5.58}, {"name": "bone128", "parent": "bone127", "length": 6.64, "rotation": -0.71, "x": 6.4}, {"name": "bone129", "parent": "bone128", "length": 6.81, "rotation": 0.02, "x": 6.64}, {"name": "bone130", "parent": "bone129", "length": 6.64, "rotation": 0.69, "x": 6.81}, {"name": "bone131", "parent": "bone130", "length": 6.56, "x": 6.64}, {"name": "bone132", "parent": "bone131", "length": 5.9, "x": 6.56}, {"name": "bone133", "parent": "bone132", "length": 5.76, "x": 6.61, "y": -0.04}, {"name": "bone134", "parent": "bone133", "length": 6.32, "rotation": -0.73, "x": 5.84, "y": -0.08}, {"name": "bone135", "parent": "bone134", "length": 5.68, "rotation": 0.73, "x": 6.32}, {"name": "bone136", "parent": "bone135", "length": 5.52, "rotation": -0.83, "x": 5.68}, {"name": "bone137", "parent": "bone136", "length": 6, "rotation": 0.83, "x": 5.52}, {"name": "bone138", "parent": "bone137", "length": 6.32, "x": 6}, {"name": "bone139", "parent": "bone138", "length": 5.76, "x": 6.32}, {"name": "bone140", "parent": "bone139", "length": 5.2, "rotation": -0.88, "x": 5.76}, {"name": "bone141", "parent": "bone140", "length": 5.68, "rotation": -0.73, "x": 5.2}, {"name": "bone142", "parent": "root"}, {"name": "bone143", "parent": "root", "length": 6.71, "rotation": 90, "x": 29.5, "y": 20.57}, {"name": "bone144", "parent": "root", "length": 11.64, "rotation": 90, "x": 0.37, "y": 1.27}, {"name": "bone153", "parent": "root", "length": 15.22, "rotation": 90, "x": -4.04, "y": -39.05}, {"name": "bone154", "parent": "bone153", "length": 41.12, "rotation": -0.37, "x": 5.07, "y": -16.55}, {"name": "bone155", "parent": "bone154", "length": 20.29, "rotation": -179.63, "x": 64.08, "y": -0.12}, {"name": "bone156", "parent": "bone154", "length": 18.42, "rotation": -0.46, "x": 62.98, "y": -0.68}, {"name": "bone157", "parent": "bone153", "length": 33.89, "rotation": 9.98, "x": 8.54, "y": 10.41}, {"name": "bone159", "parent": "bone154", "length": 16.66, "rotation": -0.04, "x": 3.42, "y": -0.71}, {"name": "bone160", "parent": "bone159", "length": 27.61, "rotation": -0.08, "x": 17.14, "y": 0.12}, {"name": "bone161", "parent": "bone160", "length": 25.94, "rotation": 0.49, "x": 27.85}, {"name": "bone145", "parent": "root", "length": 2.74, "rotation": 90, "x": -0.8, "y": 52.82}, {"name": "bone146", "parent": "root", "length": 2.74, "rotation": 90, "x": 8.9, "y": 51.77}, {"name": "bone147", "parent": "root", "length": 2.74, "rotation": 90, "x": 17.7, "y": 49.47}, {"name": "bone148", "parent": "root", "length": 2.74, "rotation": 90, "x": 26.23, "y": 45.27}, {"name": "bone149", "parent": "root", "length": 2.74, "rotation": 90, "x": 33.65, "y": 40.21}, {"name": "bone150", "parent": "root", "length": 2.74, "rotation": 90, "x": 39.66, "y": 34.21}, {"name": "bone151", "parent": "root", "length": 2.74, "rotation": 90, "x": 44.99, "y": 25.77}, {"name": "bone152", "parent": "root", "length": 2.74, "rotation": 90, "x": 49.13, "y": 17.65}, {"name": "bone158", "parent": "root", "length": 2.74, "rotation": 90, "x": 51.19, "y": 9.41}, {"name": "bone162", "parent": "root", "length": 2.74, "rotation": 90, "x": 52.1, "y": -0.02}, {"name": "bone163", "parent": "root", "length": 2.74, "rotation": 90, "x": 51.49, "y": -9.75}, {"name": "bone164", "parent": "root", "length": 2.74, "rotation": 90, "x": 49.52, "y": -18.26}, {"name": "bone165", "parent": "root", "length": 2.74, "rotation": 90, "x": 45.43, "y": -27.02}, {"name": "bone166", "parent": "root", "length": 2.74, "rotation": 90, "x": 40.47, "y": -34.03}, {"name": "bone167", "parent": "root", "length": 2.74, "rotation": 90, "x": 33.31, "y": -41.03}, {"name": "bone168", "parent": "root", "length": 2.74, "rotation": 90, "x": 26.01, "y": -45.85}, {"name": "bone169", "parent": "root", "length": 2.74, "rotation": 90, "x": 17.46, "y": -50.07}, {"name": "bone170", "parent": "root", "length": 2.74, "rotation": 90, "x": 8.81, "y": -52.07}, {"name": "bone171", "parent": "root", "length": 2.74, "rotation": 90, "x": -0.3, "y": -52.51}, {"name": "bone172", "parent": "root", "length": 2.74, "rotation": 90, "x": -9.43, "y": -52.07}, {"name": "bone173", "parent": "root", "length": 2.74, "rotation": 90, "x": -18.34, "y": -49.87}, {"name": "bone174", "parent": "root", "length": 2.74, "rotation": 90, "x": -26.7, "y": -46.24}, {"name": "bone175", "parent": "root", "length": 2.74, "rotation": 90, "x": -34.19, "y": -40.78}, {"name": "bone176", "parent": "root", "length": 2.74, "rotation": 90, "x": -40.14, "y": -34.2}, {"name": "bone177", "parent": "root", "length": 2.74, "rotation": 90, "x": -45.78, "y": -26.88}, {"name": "bone178", "parent": "root", "length": 2.74, "rotation": 90, "x": -49.54, "y": -18.26}, {"name": "bone179", "parent": "root", "length": 2.74, "rotation": 90, "x": -51.82, "y": -9.55}, {"name": "bone180", "parent": "root", "length": 2.74, "rotation": 90, "x": -52.61, "y": -0.44}, {"name": "bone181", "parent": "root", "length": 2.74, "rotation": 90, "x": -52.17, "y": 8.47}, {"name": "bone182", "parent": "root", "length": 2.74, "rotation": 90, "x": -49.86, "y": 17.38}, {"name": "bone183", "parent": "root", "length": 2.74, "rotation": 90, "x": -45.9, "y": 25.85}, {"name": "bone184", "parent": "root", "length": 2.74, "rotation": 90, "x": -40.62, "y": 33.22}, {"name": "bone185", "parent": "root", "length": 2.74, "rotation": 90, "x": -34.13, "y": 39.71}, {"name": "bone186", "parent": "root", "length": 2.74, "rotation": 90, "x": -26.54, "y": 44.99}, {"name": "bone187", "parent": "root", "length": 2.74, "rotation": 90, "x": -18.73, "y": 49.28}, {"name": "bone188", "parent": "root", "length": 2.74, "rotation": 90, "x": -9.6, "y": 51.81}], "slots": [{"name": "arrow1", "bone": "bone2"}, {"name": "arrow3", "bone": "bone11"}, {"name": "arrow5", "bone": "bone12"}, {"name": "arrow2", "bone": "bone2"}, {"name": "arrow4", "bone": "bone11"}, {"name": "arrow6", "bone": "bone12"}, {"name": "Nap tien6", "bone": "root"}, {"name": "Nap tien14", "bone": "bone10"}, {"name": "Nap tien8", "bone": "bone9"}, {"name": "Nap tien9", "bone": "bone8"}, {"name": "Nap tien10", "bone": "bone7"}, {"name": "Nap tien11", "bone": "bone6"}, {"name": "Nap tien12", "bone": "bone5"}, {"name": "Nap tien7", "bone": "bone4"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "root"}, {"name": "Nap tien2", "bone": "bone"}, {"name": "Nap tien15", "bone": "bone13"}, {"name": "Nap tien16", "bone": "bone14"}, {"name": "Nap tien17", "bone": "bone15"}, {"name": "Nap tien18", "bone": "bone16"}, {"name": "Nap tien19", "bone": "bone17"}, {"name": "Nap tien20", "bone": "bone18"}, {"name": "Nap tien21", "bone": "bone19"}, {"name": "Nap tien22", "bone": "bone20"}, {"name": "Nap tien23", "bone": "bone21"}, {"name": "Nap tien24", "bone": "bone22"}, {"name": "Nap tien25", "bone": "bone23"}, {"name": "Nap tien26", "bone": "bone24"}, {"name": "Nap tien27", "bone": "bone25"}, {"name": "Nap tien28", "bone": "bone26"}, {"name": "Nap tien29", "bone": "bone27"}, {"name": "Nap tien30", "bone": "bone28"}, {"name": "Nap tien31", "bone": "bone29"}, {"name": "Nap tien33", "bone": "bone34"}, {"name": "Nap tien32", "bone": "bone30"}, {"name": "Nap tien5", "bone": "bone33"}, {"name": "Nap tien3", "bone": "root"}, {"name": "Nap tien4", "bone": "root"}, {"name": "Nap tien13", "bone": "root"}, {"name": "backlight", "bone": "bone57"}, {"name": "star", "bone": "bone35"}, {"name": "star2", "bone": "bone36"}, {"name": "hopqua1", "bone": "root"}, {"name": "hopqua2", "bone": "root"}, {"name": "hopqua3", "bone": "root"}, {"name": "ruttien0", "bone": "root"}, {"name": "ruttien2", "bone": "bone38"}, {"name": "ruttien4", "bone": "bone41", "blend": "additive"}, {"name": "ruttien3", "bone": "bone40", "blend": "multiply"}, {"name": "ruttien1", "bone": "root"}, {"name": "star3", "bone": "bone39"}, {"name": "Jackpot Icon0", "bone": "root"}, {"name": "Jackpot Icon3", "bone": "root"}, {"name": "Jackpot Icon1", "bone": "root"}, {"name": "Nap tien34", "bone": "bone58"}, {"name": "Nap tien35", "bone": "bone59"}, {"name": "Nap tien36", "bone": "bone60"}, {"name": "Jackpot Icon4", "bone": "root"}, {"name": "Jackpot Icon10", "bone": "bone49"}, {"name": "Jackpot Icon11", "bone": "bone50"}, {"name": "Jackpot Icon5", "bone": "bone44"}, {"name": "Jackpot Icon6", "bone": "bone45"}, {"name": "Jackpot Icon7", "bone": "bone46"}, {"name": "Jackpot Icon8", "bone": "bone47"}, {"name": "Jackpot Icon9", "bone": "bone48"}, {"name": "Jackpot roll0", "bone": "bone42"}, {"name": "Jackpot roll1", "bone": "bone51"}, {"name": "Jackpot roll2", "bone": "bone52"}, {"name": "Jackpot roll3", "bone": "bone53"}, {"name": "Jackpot roll4", "bone": "bone54"}, {"name": "Jackpot roll5", "bone": "bone55"}, {"name": "Jackpot Icon border", "bone": "root"}, {"name": "Jackpot roll6", "bone": "bone56"}, {"name": "logonho3", "bone": "root"}, {"name": "logonho4", "bone": "root", "blend": "additive"}, {"name": "logonho2", "bone": "root", "blend": "additive"}, {"name": "logonho5", "bone": "root", "blend": "additive"}, {"name": "logonho1", "bone": "root"}, {"name": "logonho6", "bone": "root", "blend": "additive"}, {"name": "bongden0", "bone": "root", "attachment": "bongden0"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "bone62", "blend": "additive"}, {"name": "dassslight", "bone": "bone63"}, {"name": "light0", "bone": "root"}, {"name": "path", "bone": "bone88", "attachment": "path"}, {"name": "chiploading4", "bone": "root"}, {"name": "chiploading5", "bone": "root", "blend": "screen"}, {"name": "chiploading8", "bone": "bone89"}, {"name": "chiploading9", "bone": "bone90", "blend": "multiply"}, {"name": "chiploading10", "bone": "bone91", "blend": "additive"}, {"name": "xiu", "bone": "bone92"}, {"name": "xiu2", "bone": "bone92", "blend": "additive"}, {"name": "tai", "bone": "bone93"}, {"name": "tai2", "bone": "bone93", "blend": "additive"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "root"}, {"name": "light1", "bone": "root", "blend": "additive"}, {"name": "light2", "bone": "root", "blend": "additive"}, {"name": "moiban", "bone": "bone142", "attachment": "moiban"}, {"name": "moiban2", "bone": "bone142", "attachment": "moiban"}, {"name": "moi ban2", "bone": "root"}, {"name": "moi ban3", "bone": "bone143"}, {"name": "lvlup0", "bone": "bone144", "blend": "additive"}, {"name": "option301", "bone": "root"}, {"name": "option303", "bone": "root"}, {"name": "option302", "bone": "bone145", "blend": "additive"}, {"name": "option305", "bone": "bone146", "blend": "additive"}, {"name": "option306", "bone": "bone147", "blend": "additive"}, {"name": "option307", "bone": "bone148", "blend": "additive"}, {"name": "option308", "bone": "bone149", "blend": "additive"}, {"name": "option309", "bone": "bone150", "blend": "additive"}, {"name": "option310", "bone": "bone151", "blend": "additive"}, {"name": "option311", "bone": "bone152", "blend": "additive"}, {"name": "option312", "bone": "bone158", "blend": "additive"}, {"name": "option315", "bone": "bone162", "blend": "additive"}, {"name": "option316", "bone": "bone163", "blend": "additive"}, {"name": "option317", "bone": "bone164", "blend": "additive"}, {"name": "option318", "bone": "bone165", "blend": "additive"}, {"name": "option319", "bone": "bone166", "blend": "additive"}, {"name": "option320", "bone": "bone167", "blend": "additive"}, {"name": "option321", "bone": "bone168", "blend": "additive"}, {"name": "option322", "bone": "bone169", "blend": "additive"}, {"name": "option323", "bone": "bone170", "blend": "additive"}, {"name": "option324", "bone": "bone171", "blend": "additive"}, {"name": "option325", "bone": "bone172", "blend": "additive"}, {"name": "option326", "bone": "bone173", "blend": "additive"}, {"name": "option327", "bone": "bone174", "blend": "additive"}, {"name": "option328", "bone": "bone175", "blend": "additive"}, {"name": "option329", "bone": "bone176", "blend": "additive"}, {"name": "option330", "bone": "bone177", "blend": "additive"}, {"name": "option331", "bone": "bone178", "blend": "additive"}, {"name": "option332", "bone": "bone179", "blend": "additive"}, {"name": "option333", "bone": "bone180", "blend": "additive"}, {"name": "option334", "bone": "bone181", "blend": "additive"}, {"name": "option335", "bone": "bone182", "blend": "additive"}, {"name": "option336", "bone": "bone183", "blend": "additive"}, {"name": "option337", "bone": "bone184", "blend": "additive"}, {"name": "option338", "bone": "bone185", "blend": "additive"}, {"name": "option339", "bone": "bone186", "blend": "additive"}, {"name": "option340", "bone": "bone187", "blend": "additive"}, {"name": "option341", "bone": "bone188", "blend": "additive"}, {"name": "option313", "bone": "root", "blend": "additive"}, {"name": "option314", "bone": "root", "blend": "additive"}, {"name": "option304", "bone": "root", "blend": "additive"}], "path": [{"name": "moiban", "order": 1, "bones": ["bone94", "bone95", "bone96", "bone97", "bone98", "bone99", "bone100", "bone101", "bone102", "bone103", "bone104", "bone105", "bone106", "bone107", "bone108", "bone109", "bone110", "bone111", "bone112", "bone113", "bone114", "bone115", "bone116", "bone117"], "target": "moiban", "rotateMode": "chainScale", "position": 0.5}, {"name": "moiban2", "order": 2, "bones": ["bone118", "bone119", "bone120", "bone121", "bone122", "bone123", "bone124", "bone125", "bone126", "bone127", "bone128", "bone129", "bone130", "bone131", "bone132", "bone133", "bone134", "bone135", "bone136", "bone137", "bone138", "bone139", "bone140", "bone141"], "target": "moiban2", "rotateMode": "chainScale"}, {"name": "path", "order": 0, "bones": ["bone87", "bone86", "bone85", "bone84", "bone83", "bone82", "bone81", "bone80", "bone79", "bone78", "bone77", "bone76", "bone75", "bone74", "bone73", "bone72", "bone71", "bone70", "bone69", "bone68", "bone67", "bone66", "bone65", "bone64"], "target": "path", "rotateMode": "chainScale"}], "skins": [{"name": "default", "attachments": {"Jackpot Icon border": {"Jackpot Icon border": {"x": -0.13, "y": -19.91, "width": 111, "height": 33}}, "Jackpot Icon0": {"Jackpot Icon0": {"width": 112, "height": 81}}, "Jackpot Icon1": {"Jackpot Icon1": {"width": 58, "height": 81}}, "Jackpot Icon10": {"Jackpot Icon10": {"x": 0.27, "y": 0.22, "rotation": -90, "width": 15, "height": 15}}, "Jackpot Icon11": {"Jackpot Icon11": {"x": 0.27, "y": 0.47, "rotation": -90, "width": 12, "height": 15}}, "Jackpot Icon3": {"Jackpot Icon3": {"x": 0.48, "y": 11.49, "width": 18, "height": 18}}, "Jackpot Icon4": {"Jackpot Icon4": {"x": -0.05, "y": -19.93, "width": 111, "height": 33}}, "Jackpot Icon5": {"Jackpot Icon5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-7.66, -2.29, -7.66, 5.71, 7.34, 5.71, 7.34, -2.29], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 8, "height": 15}}, "Jackpot Icon6": {"Jackpot Icon6": {"x": -0.1, "y": 0.18, "rotation": -90, "width": 13, "height": 15}}, "Jackpot Icon7": {"Jackpot Icon7": {"x": 0.16, "y": 0.71, "rotation": -90, "width": 11, "height": 15}}, "Jackpot Icon8": {"Jackpot Icon8": {"x": 0.16, "y": -0.53, "rotation": -90, "width": 13, "height": 15}}, "Jackpot Icon9": {"Jackpot Icon9": {"x": 0.29, "y": -0.25, "rotation": -90, "width": 10, "height": 15}}, "Jackpot roll0": {"Jackpot roll0": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll1": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll2": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll3": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll4": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll5": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll6": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll7": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll8": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll9": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll10": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll11": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll12": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll13": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll14": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}}, "Jackpot roll1": {"Jackpot roll0": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll1": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll2": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll3": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll4": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll5": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll6": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll7": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll8": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll9": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll10": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll11": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll12": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll13": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll14": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}}, "Jackpot roll2": {"Jackpot roll0": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll1": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll2": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll3": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll4": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll5": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll6": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll7": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll8": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll9": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll10": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll11": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll12": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll13": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll14": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}}, "Jackpot roll3": {"Jackpot roll0": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll1": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll2": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll3": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll4": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll5": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll6": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll7": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll8": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll9": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll10": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll11": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll12": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll13": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll14": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}}, "Jackpot roll4": {"Jackpot roll0": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll1": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll2": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll3": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll4": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll5": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll6": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll7": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll8": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll9": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll10": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll11": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll12": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll13": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll14": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}}, "Jackpot roll5": {"Jackpot roll0": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll1": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll2": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll3": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll4": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll5": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll6": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll7": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll8": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll9": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll10": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll11": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll12": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll13": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll14": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}}, "Jackpot roll6": {"Jackpot roll0": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll1": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll2": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll3": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll4": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll5": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll6": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll7": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll8": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll9": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll10": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll11": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll12": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll13": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}, "Jackpot roll14": {"x": 9.34, "y": -0.19, "rotation": -90, "width": 13, "height": 21}}, "Nap tien10": {"Nap tien10": {"x": 0.76, "y": 0.04, "rotation": -90, "width": 48, "height": 52}}, "Nap tien11": {"Nap tien11": {"x": 0.48, "y": -0.34, "rotation": -90, "width": 46, "height": 52}}, "Nap tien12": {"Nap tien12": {"x": -2.11, "y": 0.33, "rotation": -90, "width": 54, "height": 61}}, "Nap tien13": {"Nap tien4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 32, 41.12, -16.09, 1, 1, 32, 11.12, -16.09, 1, 1, 32, 11.12, 18.91, 1, 1, 32, 41.12, 18.91, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 35}}, "Nap tien14": {"Nap tien7": {"x": 0.43, "y": -1.16, "rotation": -90, "width": 51, "height": 53}}, "Nap tien15": {"Nap tien2": {"rotation": -90, "width": 13, "height": 13}}, "Nap tien16": {"Nap tien2": {"rotation": -90, "width": 13, "height": 13}}, "Nap tien17": {"Nap tien2": {"rotation": -90, "width": 13, "height": 13}}, "Nap tien18": {"Nap tien2": {"rotation": -90, "width": 13, "height": 13}}, "Nap tien19": {"Nap tien2": {"rotation": -90, "width": 13, "height": 13}}, "Nap tien2": {"Nap tien2": {"rotation": -90, "width": 13, "height": 13}}, "Nap tien20": {"Nap tien2": {"rotation": -90, "width": 13, "height": 13}}, "Nap tien21": {"Nap tien2": {"rotation": -90, "width": 13, "height": 13}}, "Nap tien22": {"Nap tien2": {"rotation": -90, "width": 13, "height": 13}}, "Nap tien23": {"Nap tien2": {"rotation": -90, "width": 13, "height": 13}}, "Nap tien24": {"Nap tien2": {"rotation": -90, "width": 13, "height": 13}}, "Nap tien25": {"Nap tien2": {"rotation": -90, "width": 13, "height": 13}}, "Nap tien26": {"Nap tien2": {"rotation": -90, "width": 13, "height": 13}}, "Nap tien27": {"Nap tien2": {"rotation": -90, "width": 13, "height": 13}}, "Nap tien28": {"Nap tien2": {"rotation": -90, "width": 13, "height": 13}}, "Nap tien29": {"Nap tien2": {"rotation": -90, "width": 13, "height": 13}}, "Nap tien3": {"Nap tien3": {"type": "mesh", "uvs": [0, 0.21714, 0.02054, 0.285, 0.17183, 0.28249, 0.21348, 0.33779, 0.39809, 0.9536, 0.44389, 1, 0.57021, 1, 0.98107, 0.87316, 1, 0.50368, 1, 0.15933, 0.96025, 0.10152, 0.34257, 0.20708, 0.27177, 0.00098, 0.20237, 0, 0, 0.03114], "triangles": [0, 14, 13, 2, 0, 13, 11, 2, 13, 11, 13, 12, 1, 0, 2, 3, 2, 11, 10, 9, 8, 11, 10, 8, 11, 4, 3, 11, 8, 4, 8, 6, 4, 6, 5, 4, 8, 7, 6], "vertices": [1, 31, -54.78, 46.3, 1, 1, 31, -52.61, 41, 1, 1, 31, -32.45, 38.89, 1, 1, 31, -27.37, 34.19, 1, 1, 31, -7.95, -13.88, 1, 1, 31, -2.24, -17.99, 1, 1, 31, 14.58, -19.91, 1, 1, 31, 70.35, -16.81, 1, 1, 31, 75.96, 10.07, 1, 1, 31, 78.84, 35.38, 1, 1, 31, 74.04, 40.24, 1, 1, 31, -9.09, 41.85, 1, 1, 31, -16.78, 58.07, 1, 1, 31, -26.02, 59.2, 1, 1, 31, -53.22, 59.98, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28], "width": 134, "height": 74}}, "Nap tien30": {"Nap tien2": {"rotation": -90, "width": 13, "height": 13}}, "Nap tien31": {"Nap tien2": {"rotation": -90, "width": 13, "height": 13}}, "Nap tien32": {"Nap tien2": {"rotation": -90, "width": 13, "height": 13}}, "Nap tien33": {"Nap tien5": {"x": -0.85, "y": -0.22, "rotation": -91.18, "width": 44, "height": 44}}, "Nap tien34": {"Nap tien5": {"x": -0.56, "y": -0.13, "rotation": -90.23, "width": 44, "height": 44}}, "Nap tien35": {"Nap tien5": {"x": -0.56, "y": -0.13, "rotation": -90.23, "width": 44, "height": 44}}, "Nap tien36": {"Nap tien5": {"x": -0.56, "y": -0.13, "rotation": -90.23, "width": 44, "height": 44}}, "Nap tien4": {"Nap tien4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 32, -5.47, -24, 1, 1, 32, -35.47, -24, 1, 1, 32, -35.47, 11, 1, 1, 32, -5.47, 11, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 35}}, "Nap tien5": {"Nap tien5": {"x": -0.85, "y": -0.22, "rotation": -91.18, "width": 44, "height": 44}}, "Nap tien6": {"Nap tien6": {"x": -20.07, "y": 0.36, "width": 275, "height": 85}}, "Nap tien7": {"Nap tien7": {"x": 1.03, "y": 0.41, "rotation": -90, "width": 51, "height": 53}}, "Nap tien8": {"Nap tien8": {"x": 6.8, "y": 0.76, "rotation": -90, "width": 48, "height": 66}}, "Nap tien9": {"Nap tien9": {"x": 1.14, "y": -0.07, "rotation": -90, "width": 32, "height": 52}}, "arrow1": {"arrow1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.14, -17.18, -10.86, -17.18, -10.86, 17.82, 8.14, 17.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 19, "height": 35}}, "arrow2": {"arrow2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [12.14, -23.33, -15.86, -23.33, -15.86, 23.67, 12.14, 23.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 28, "height": 47}}, "arrow3": {"arrow1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.14, -17.18, -10.86, -17.18, -10.86, 17.82, 8.14, 17.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 19, "height": 35}}, "arrow4": {"arrow2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [12.14, -23.33, -15.86, -23.33, -15.86, 23.67, 12.14, 23.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 28, "height": 47}}, "arrow5": {"arrow1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.14, -17.18, -10.86, -17.18, -10.86, 17.82, 8.14, 17.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 19, "height": 35}}, "arrow6": {"arrow2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [12.14, -23.33, -15.86, -23.33, -15.86, 23.67, 12.14, 23.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 28, "height": 47}}, "backlight": {"backlight": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.5, 0, 0, 0.5, 0, 1, 0, 1, 0.5, 0.5, 0.5], "triangles": [0, 7, 1, 1, 7, 8, 7, 6, 8, 8, 6, 5, 1, 8, 2, 2, 8, 3, 8, 5, 3, 3, 5, 4], "vertices": [-118.1, -116.94, -118.1, -2.94, -118.1, 111.06, -5.6, 111.06, 106.9, 111.06, 106.9, -2.94, 106.9, -116.94, -5.6, -116.94, -5.6, -2.94], "hull": 8, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 0], "width": 228, "height": 225}}, "bongden0": {"bongden0": {"width": 124, "height": 124}}, "chiploading10": {"chiploading5": {"x": 19.52, "y": 0.01, "rotation": -90, "width": 35, "height": 39}, "chiploading6": {"x": 19.02, "y": 0.51, "rotation": -90, "width": 36, "height": 36}, "chiploading7": {"x": 19.02, "y": 0.01, "rotation": -90, "width": 35, "height": 36}, "chiploading8": {"x": 18.12, "y": 0.51, "rotation": -90, "width": 36, "height": 32}}, "chiploading4": {"chiploading0": {"x": 0.5, "width": 13, "height": 102}, "chiploading1": {"x": 0.5, "y": -0.19, "width": 37, "height": 103}, "chiploading2": {"y": 0.5, "width": 66, "height": 103}, "chiploading3": {"y": 0.5, "width": 88, "height": 101}, "chiploading4": {"x": 0.5, "y": 0.5, "width": 101, "height": 101}, "chiploading5": {"path": "chiploading1", "x": 0.5, "y": -0.6, "scaleX": -1, "width": 37, "height": 103}, "chiploading6": {"path": "chiploading2", "y": 0.5, "scaleX": -1, "width": 66, "height": 103}, "chiploading7": {"path": "chiploading3", "y": 0.5, "scaleX": -0.998, "width": 88, "height": 101}}, "chiploading5": {"chiploading0": {"x": 0.5, "width": 13, "height": 102}, "chiploading1": {"x": 0.5, "y": -0.19, "width": 37, "height": 103}, "chiploading2": {"y": 0.5, "width": 66, "height": 103}, "chiploading3": {"y": 0.5, "width": 88, "height": 101}, "chiploading4": {"x": 0.5, "y": 0.5, "width": 101, "height": 101}, "chiploading5": {"path": "chiploading1", "x": 0.5, "y": -0.6, "scaleX": -1, "width": 37, "height": 103}, "chiploading6": {"path": "chiploading2", "y": 0.5, "scaleX": -1, "width": 66, "height": 103}, "chiploading7": {"path": "chiploading3", "y": 0.5, "scaleX": -0.998, "width": 88, "height": 101}}, "chiploading8": {"chiploading5": {"x": 19.52, "y": 0.01, "rotation": -90, "width": 35, "height": 39}, "chiploading6": {"x": 19.02, "y": 0.51, "rotation": -90, "width": 36, "height": 36}, "chiploading7": {"x": 19.02, "y": 0.01, "rotation": -90, "width": 35, "height": 36}, "chiploading8": {"x": 18.12, "y": 0.51, "rotation": -90, "width": 36, "height": 32}}, "chiploading9": {"chiploading5": {"x": 19.52, "y": 0.01, "rotation": -90, "width": 35, "height": 39}, "chiploading6": {"x": 19.02, "y": 0.51, "rotation": -90, "width": 36, "height": 36}, "chiploading7": {"x": 19.02, "y": 0.01, "rotation": -90, "width": 35, "height": 36}, "chiploading8": {"x": 18.12, "y": 0.51, "rotation": -90, "width": 36, "height": 32}}, "dassslight": {"dassslight": {"x": -17.77, "y": -0.18, "width": 270, "height": 20}}, "dennaptien": {"dennaptien": {"x": -19.27, "y": 0.17, "width": 274, "height": 85}}, "hopqua1": {"hopqua1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [72.44, -55.48, -87.56, -55.48, -87.56, 106.52, 72.44, 106.52], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 162}}, "hopqua2": {"hopqua2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [43.5, -42.5, -43.5, -42.5, -43.5, 42.5, 43.5, 42.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 87, "height": 85}}, "hopqua3": {"hopqua3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 37, 99.25, -39.97, 1, 1, 37, -42.66, -78.34, 1, 1, 37, -74.51, 39.43, 1, 1, 37, 67.4, 77.8, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 147, "height": 122}}, "light0": {"light0": {"type": "mesh", "uvs": [1, 1, 0.95833, 1, 0.91667, 1, 0.875, 1, 0.83333, 1, 0.79167, 1, 0.75, 1, 0.70833, 1, 0.66667, 1, 0.625, 1, 0.58333, 1, 0.54167, 1, 0.5, 1, 0.45833, 1, 0.41667, 1, 0.375, 1, 0.33333, 1, 0.29167, 1, 0.25, 1, 0.20833, 1, 0.16667, 1, 0.125, 1, 0.08333, 1, 0.04167, 1, 0, 1, 0, 0, 0.04167, 0, 0.08333, 0, 0.125, 0, 0.16667, 0, 0.20833, 0, 0.25, 0, 0.29167, 0, 0.33333, 0, 0.375, 0, 0.41667, 0, 0.45833, 0, 0.5, 0, 0.54167, 0, 0.58333, 0, 0.625, 0, 0.66667, 0, 0.70833, 0, 0.75, 0, 0.79167, 0, 0.83333, 0, 0.875, 0, 0.91667, 0, 0.95833, 0, 1, 0], "triangles": [24, 26, 25, 23, 26, 24, 23, 27, 26, 22, 27, 23, 22, 28, 27, 21, 28, 22, 21, 29, 28, 20, 29, 21, 20, 30, 29, 19, 30, 20, 19, 31, 30, 18, 31, 19, 18, 32, 31, 17, 32, 18, 17, 33, 32, 16, 33, 17, 16, 34, 33, 15, 34, 16, 15, 35, 34, 14, 35, 15, 14, 36, 35, 13, 36, 14, 13, 37, 36, 12, 37, 13, 12, 38, 37, 11, 38, 12, 11, 39, 38, 10, 39, 11, 10, 40, 39, 9, 40, 10, 9, 41, 40, 8, 41, 9, 8, 42, 41, 7, 42, 8, 7, 43, 42, 6, 43, 7, 6, 44, 43, 5, 44, 6, 5, 45, 44, 4, 45, 5, 4, 46, 45, 3, 46, 4, 3, 47, 46, 2, 47, 3, 2, 48, 47, 1, 48, 2, 1, 49, 48, 0, 49, 1], "vertices": [2, 64, -0.04, 3.4, 0.99997, 66, -7.91, 3.25, 3e-05, 3, 64, 6.25, 3.4, 0.03362, 65, 2.57, 3.34, 0.76886, 66, -1.62, 3.31, 0.19752, 3, 65, 8.86, 3.48, 0.00439, 66, 4.67, 3.38, 0.43597, 67, 0.03, 3.38, 0.55964, 3, 67, 6.32, 3.51, 0.14334, 68, 1.68, 3.5, 0.84092, 69, -3.49, 3.6, 0.01574, 3, 68, 7.97, 3.62, 0.05385, 69, 2.8, 3.54, 0.92312, 70, -2.9, 3.5, 0.02303, 3, 69, 9.09, 3.49, 0.02427, 70, 3.39, 3.55, 0.93852, 71, -2.76, 3.59, 0.0372, 3, 70, 9.68, 3.59, 0.01618, 71, 3.53, 3.54, 0.90419, 72, -2.48, 3.49, 0.07963, 3, 71, 9.83, 3.49, 0.01151, 72, 3.82, 3.45, 0.97373, 73, -3.33, 3.4, 0.01475, 3, 72, 10.11, 3.41, 0.03607, 73, 2.96, 3.45, 0.94461, 74, -3.12, 3.43, 0.01931, 3, 73, 9.25, 3.5, 0.02211, 74, 3.17, 3.51, 0.97233, 75, -4.12, 3.62, 0.00556, 2, 74, 9.46, 3.59, 0.11018, 75, 2.17, 3.54, 0.88982, 2, 75, 8.47, 3.46, 0.27751, 76, 0.9, 3.39, 0.72249, 2, 76, 7.19, 3.48, 0.39629, 77, 0.44, 3.48, 0.60371, 2, 77, 6.74, 3.52, 0.50766, 78, 0.12, 3.52, 0.49234, 2, 78, 6.42, 3.62, 0.33264, 79, 0.57, 3.62, 0.66736, 2, 79, 6.87, 3.66, 0.6688, 80, -0.68, 3.66, 0.3312, 2, 80, 5.61, 3.66, 0.73915, 81, -1.11, 3.66, 0.26085, 2, 81, 5.18, 3.66, 0.96301, 82, -3.25, 3.6, 0.03699, 2, 81, 11.48, 3.66, 0.04687, 82, 3.04, 3.72, 0.95313, 2, 82, 9.33, 3.84, 0.04609, 83, 1.58, 3.76, 0.95391, 2, 83, 7.87, 3.8, 0.5004, 84, 0.14, 3.8, 0.4996, 2, 84, 6.43, 3.8, 0.62317, 85, -0.65, 3.8, 0.37683, 2, 85, 5.64, 3.84, 0.78023, 86, -1.47, 3.85, 0.21977, 3, 85, 11.94, 3.88, 0.00124, 86, 4.82, 3.85, 0.87969, 87, -1.94, 3.83, 0.11907, 1, 87, 4.36, 3.89, 1, 2, 86, 11.11, -4.02, 0.01277, 87, 4.42, -3.99, 0.98723, 3, 85, 11.99, -3.99, 0.00387, 86, 4.82, -4.02, 0.88322, 87, -1.87, -4.04, 0.1129, 2, 85, 5.7, -4.03, 0.74282, 86, -1.47, -4.02, 0.25718, 2, 84, 6.43, -4.07, 0.59738, 85, -0.59, -4.07, 0.40262, 2, 83, 7.92, -4.07, 0.4836, 84, 0.14, -4.07, 0.5164, 2, 82, 9.48, -4.03, 0.17353, 83, 1.62, -4.11, 0.82647, 3, 81, 11.48, -4.21, 0.06866, 82, 3.19, -4.15, 0.87088, 83, -4.67, -4.15, 0.06046, 3, 80, 11.91, -4.21, 0.00103, 81, 5.18, -4.21, 0.94737, 82, -3.1, -4.27, 0.0516, 2, 80, 5.61, -4.21, 0.72949, 81, -1.11, -4.21, 0.27051, 2, 79, 6.92, -4.21, 0.64466, 80, -0.68, -4.21, 0.35534, 3, 77, 13.08, -4.31, 0.00374, 78, 6.54, -4.25, 0.40185, 79, 0.62, -4.25, 0.59441, 2, 77, 6.79, -4.35, 0.52331, 78, 0.25, -4.35, 0.47669, 2, 76, 7.3, -4.39, 0.34887, 77, 0.5, -4.4, 0.65113, 2, 75, 8.37, -4.41, 0.32838, 76, 1.01, -4.48, 0.67162, 3, 74, 9.56, -4.28, 0.11704, 75, 2.08, -4.33, 0.88008, 76, -5.28, -4.57, 0.00288, 3, 73, 9.31, -4.37, 0.09311, 74, 3.27, -4.36, 0.87477, 75, -4.22, -4.25, 0.03212, 3, 72, 10.06, -4.47, 0.07512, 73, 3.02, -4.42, 0.85601, 74, -3.02, -4.44, 0.06887, 3, 71, 9.76, -4.38, 0.05369, 72, 3.76, -4.42, 0.90107, 73, -3.27, -4.47, 0.04524, 3, 70, 9.74, -4.28, 0.0596, 71, 3.47, -4.33, 0.79454, 72, -2.53, -4.38, 0.14585, 3, 69, 9.02, -4.38, 0.04016, 70, 3.45, -4.33, 0.87146, 71, -2.82, -4.28, 0.08838, 3, 68, 8.11, -4.25, 0.12401, 69, 2.73, -4.33, 0.78434, 70, -2.84, -4.37, 0.09165, 3, 67, 6.48, -4.36, 0.20832, 68, 1.82, -4.37, 0.73778, 69, -3.56, -4.28, 0.0539, 4, 65, 9.04, -4.39, 0.01721, 66, 4.75, -4.49, 0.40198, 67, 0.19, -4.49, 0.56877, 68, -4.47, -4.48, 0.01204, 4, 64, 6.25, -4.47, 0.12113, 65, 2.75, -4.53, 0.62209, 66, -1.54, -4.56, 0.25557, 67, -6.1, -4.62, 0.00121, 2, 64, -0.04, -4.47, 0.89197, 65, -3.54, -4.67, 0.10803], "hull": 50, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 0], "width": 151, "height": 17}}, "light1": {"light0": {"type": "mesh", "uvs": [1, 1, 0.95833, 1, 0.91667, 1, 0.875, 1, 0.83333, 1, 0.79167, 1, 0.75, 1, 0.70833, 1, 0.66667, 1, 0.625, 1, 0.58333, 1, 0.54167, 1, 0.5, 1, 0.45833, 1, 0.41667, 1, 0.375, 1, 0.33333, 1, 0.29167, 1, 0.25, 1, 0.20833, 1, 0.16667, 1, 0.125, 1, 0.08333, 1, 0.04167, 1, 0, 1, 0, 0, 0.04167, 0, 0.08333, 0, 0.125, 0, 0.16667, 0, 0.20833, 0, 0.25, 0, 0.29167, 0, 0.33333, 0, 0.375, 0, 0.41667, 0, 0.45833, 0, 0.5, 0, 0.54167, 0, 0.58333, 0, 0.625, 0, 0.66667, 0, 0.70833, 0, 0.75, 0, 0.79167, 0, 0.83333, 0, 0.875, 0, 0.91667, 0, 0.95833, 0, 1, 0], "triangles": [24, 26, 25, 23, 26, 24, 23, 27, 26, 22, 27, 23, 22, 28, 27, 21, 28, 22, 21, 29, 28, 20, 29, 21, 20, 30, 29, 19, 30, 20, 19, 31, 30, 18, 31, 19, 18, 32, 31, 17, 32, 18, 17, 33, 32, 16, 33, 17, 16, 34, 33, 15, 34, 16, 15, 35, 34, 14, 35, 15, 14, 36, 35, 13, 36, 14, 13, 37, 36, 12, 37, 13, 12, 38, 37, 11, 38, 12, 11, 39, 38, 10, 39, 11, 10, 40, 39, 9, 40, 10, 9, 41, 40, 8, 41, 9, 8, 42, 41, 7, 42, 8, 7, 43, 42, 6, 43, 7, 6, 44, 43, 5, 44, 6, 5, 45, 44, 4, 45, 5, 4, 46, 45, 3, 46, 4, 3, 47, 46, 2, 47, 3, 2, 48, 47, 1, 48, 2, 1, 49, 48, 0, 49, 1], "vertices": [2, 94, -0.96, 7.32, 0.94006, 95, -7.29, 7.3, 0.05994, 4, 94, 5.33, 7.4, 0.56622, 95, -1, 7.4, 0.37977, 96, -6.38, 7.49, 0.05281, 97, -11.1, 7.67, 0.00121, 5, 94, 11.62, 7.48, 0.05496, 95, 5.29, 7.49, 0.41318, 96, -0.09, 7.49, 0.4163, 97, -4.81, 7.57, 0.11179, 98, -10.01, 7.41, 0.00377, 5, 95, 11.58, 7.59, 0.03261, 96, 6.2, 7.49, 0.27863, 97, 1.48, 7.47, 0.50836, 98, -3.72, 7.41, 0.1792, 99, -9.38, 7.41, 0.0012, 5, 96, 12.49, 7.49, 0.01308, 97, 7.77, 7.37, 0.2073, 98, 2.57, 7.41, 0.62562, 99, -3.09, 7.41, 0.1465, 100, -8.84, 7.29, 0.0075, 5, 97, 14.07, 7.27, 0.00523, 98, 8.86, 7.41, 0.22685, 99, 3.2, 7.41, 0.50471, 100, -2.55, 7.38, 0.25385, 101, -8.52, 7.49, 0.00936, 5, 98, 15.15, 7.41, 0.00739, 99, 9.5, 7.41, 0.12082, 100, 3.74, 7.46, 0.5998, 101, -2.23, 7.49, 0.25672, 102, -7.89, 7.49, 0.01527, 4, 100, 10.03, 7.55, 0.1387, 101, 4.06, 7.49, 0.53571, 102, -1.6, 7.49, 0.27848, 103, -7.17, 7.49, 0.04711, 5, 100, 16.32, 7.63, 0.00011, 101, 10.35, 7.49, 0.12557, 102, 4.7, 7.49, 0.41779, 103, -0.88, 7.49, 0.43806, 104, -7.37, 7.4, 0.01847, 5, 101, 16.64, 7.49, 0.00135, 102, 10.99, 7.49, 0.05182, 103, 5.41, 7.49, 0.58377, 104, -1.08, 7.48, 0.3408, 105, -7.72, 7.48, 0.02226, 4, 103, 11.7, 7.49, 0.09042, 104, 5.21, 7.56, 0.5495, 105, -1.43, 7.56, 0.35568, 106, -8.14, 7.66, 0.0044, 4, 104, 11.5, 7.64, 0.10161, 105, 4.86, 7.63, 0.63201, 106, -1.85, 7.66, 0.25084, 107, -8.49, 7.66, 0.01554, 5, 104, 17.8, 7.71, 0.00036, 105, 11.16, 7.71, 0.12261, 106, 4.44, 7.66, 0.59005, 107, -2.2, 7.66, 0.28351, 108, -8.76, 7.66, 0.00347, 5, 105, 17.45, 7.79, 0.00059, 106, 10.73, 7.66, 0.15198, 107, 4.09, 7.66, 0.62421, 108, -2.47, 7.66, 0.21435, 109, -9.08, 7.7, 0.00887, 5, 106, 17.03, 7.66, 0.00328, 107, 10.38, 7.66, 0.16576, 108, 3.82, 7.66, 0.5557, 109, -2.79, 7.7, 0.26831, 110, -8.73, 7.67, 0.00694, 5, 107, 16.67, 7.66, 0.00233, 108, 10.12, 7.66, 0.10933, 109, 3.5, 7.7, 0.63007, 110, -2.43, 7.75, 0.24982, 111, -8.66, 7.86, 0.00845, 5, 108, 16.41, 7.66, 0.00016, 109, 9.79, 7.7, 0.14132, 110, 3.86, 7.83, 0.59723, 111, -2.36, 7.86, 0.24375, 112, -8.16, 7.74, 0.01754, 5, 109, 16.09, 7.7, 0.00091, 110, 10.15, 7.91, 0.13988, 111, 3.93, 7.86, 0.55018, 112, -1.87, 7.83, 0.27097, 113, -7.27, 7.94, 0.03807, 5, 110, 16.44, 7.99, 0.00086, 111, 10.22, 7.86, 0.13449, 112, 4.42, 7.92, 0.46042, 113, -0.98, 7.94, 0.35553, 114, -6.98, 7.94, 0.0487, 6, 111, 16.51, 7.86, 0.00226, 112, 10.72, 8.01, 0.07861, 113, 5.31, 7.94, 0.45332, 114, -0.69, 7.94, 0.43508, 115, -7.01, 7.94, 0.03008, 116, -12.89, 7.74, 0.00064, 5, 113, 11.6, 7.94, 0.05585, 114, 5.6, 7.94, 0.53463, 115, -0.72, 7.94, 0.33563, 116, -6.6, 7.84, 0.07387, 117, -11.9, 7.69, 3e-05, 4, 114, 11.89, 7.94, 0.08264, 115, 5.57, 7.94, 0.38994, 116, -0.31, 7.93, 0.43742, 117, -5.61, 7.86, 0.09, 3, 115, 11.87, 7.94, 0.04654, 116, 5.98, 8.03, 0.34639, 117, 0.68, 8.04, 0.60706, 2, 116, 12.27, 8.13, 0.03167, 117, 6.97, 8.22, 0.96833, 1, 117, 13.26, 8.39, 1, 1, 117, 13.74, -8.6, 1, 3, 115, 18.16, -9.06, 9e-05, 116, 12.53, -8.87, 0.04826, 117, 7.45, -8.78, 0.95165, 4, 114, 18.19, -9.06, 0.00122, 115, 11.87, -9.06, 0.06556, 116, 6.24, -8.97, 0.33906, 117, 1.16, -8.95, 0.59416, 5, 113, 17.89, -9.06, 0.0003, 114, 11.89, -9.06, 0.10254, 115, 5.57, -9.06, 0.40472, 116, -0.05, -9.06, 0.38299, 117, -5.13, -9.13, 0.10945, 6, 112, 17.25, -8.89, 0.00191, 113, 11.6, -9.06, 0.07632, 114, 5.6, -9.06, 0.50292, 115, -0.72, -9.06, 0.3459, 116, -6.34, -9.16, 0.07241, 117, -11.42, -9.31, 0.00054, 6, 111, 16.51, -9.14, 0.00358, 112, 10.96, -8.98, 0.10319, 113, 5.31, -9.06, 0.40531, 114, -0.69, -9.06, 0.43988, 115, -7.01, -9.06, 0.04718, 116, -12.63, -9.26, 0.00085, 5, 110, 16.65, -9.01, 0.01454, 111, 10.22, -9.14, 0.12026, 112, 4.67, -9.08, 0.43553, 113, -0.98, -9.06, 0.34943, 114, -6.98, -9.06, 0.08024, 6, 109, 16.09, -9.3, 0.01424, 110, 10.36, -9.09, 0.20546, 111, 3.93, -9.14, 0.40692, 112, -1.62, -9.17, 0.3237, 113, -7.27, -9.06, 0.04855, 114, -13.27, -9.06, 0.00113, 5, 108, 16.41, -9.34, 0.00221, 109, 9.79, -9.3, 0.1936, 110, 4.07, -9.17, 0.54954, 111, -2.36, -9.14, 0.20696, 112, -7.91, -9.26, 0.0477, 6, 107, 16.68, -9.34, 0.01909, 108, 10.12, -9.34, 0.11564, 109, 3.5, -9.3, 0.55181, 110, -2.22, -9.25, 0.29684, 111, -8.66, -9.14, 0.0163, 112, -14.2, -9.35, 0.00032, 5, 106, 17.03, -9.34, 0.00709, 107, 10.38, -9.34, 0.21165, 108, 3.82, -9.34, 0.43566, 109, -2.79, -9.3, 0.30431, 110, -8.51, -9.33, 0.04129, 6, 105, 17.65, -9.21, 0.01698, 106, 10.73, -9.34, 0.15907, 107, 4.09, -9.34, 0.55061, 108, -2.47, -9.34, 0.23669, 109, -9.08, -9.3, 0.03621, 110, -14.8, -9.41, 0.00043, 5, 104, 18.01, -9.29, 0.00424, 105, 11.36, -9.29, 0.19731, 106, 4.44, -9.34, 0.48907, 107, -2.2, -9.34, 0.28791, 108, -8.76, -9.34, 0.02147, 5, 103, 17.99, -9.51, 0.00638, 104, 11.71, -9.36, 0.12809, 105, 5.07, -9.36, 0.56494, 106, -1.85, -9.34, 0.27258, 107, -8.49, -9.34, 0.028, 5, 102, 17.28, -9.51, 0.01061, 103, 11.7, -9.51, 0.12844, 104, 5.42, -9.44, 0.48037, 105, -1.22, -9.44, 0.35579, 106, -8.14, -9.34, 0.02478, 5, 101, 16.64, -9.51, 0.00536, 102, 10.99, -9.51, 0.14438, 103, 5.41, -9.51, 0.4468, 104, -0.87, -9.52, 0.36277, 105, -7.51, -9.52, 0.04069, 5, 100, 16.55, -9.37, 0.01086, 101, 10.35, -9.51, 0.13232, 102, 4.7, -9.51, 0.47688, 103, -0.88, -9.51, 0.32273, 104, -7.16, -9.6, 0.05722, 6, 99, 15.79, -9.59, 0.02328, 100, 10.26, -9.45, 0.16663, 101, 4.06, -9.51, 0.43359, 102, -1.6, -9.51, 0.33495, 103, -7.17, -9.51, 0.04141, 104, -13.45, -9.67, 0.00015, 6, 97, 20.08, -9.83, 9e-05, 98, 15.15, -9.59, 0.00551, 99, 9.5, -9.59, 0.2174, 100, 3.97, -9.54, 0.46086, 101, -2.23, -9.51, 0.26399, 102, -7.89, -9.51, 0.05216, 7, 96, 18.78, -9.51, 0.00021, 97, 13.79, -9.73, 0.03525, 98, 8.86, -9.59, 0.14966, 99, 3.2, -9.59, 0.51584, 100, -2.32, -9.62, 0.262, 101, -8.52, -9.51, 0.03677, 102, -14.18, -9.51, 0.00026, 6, 95, 18.13, -9.32, 0.00218, 96, 12.49, -9.51, 0.04549, 97, 7.5, -9.63, 0.2573, 98, 2.57, -9.59, 0.38104, 99, -3.09, -9.59, 0.27859, 100, -8.62, -9.71, 0.0354, 6, 94, 18.13, -9.44, 9e-05, 95, 11.84, -9.41, 0.0881, 96, 6.2, -9.51, 0.3002, 97, 1.21, -9.53, 0.41966, 98, -3.72, -9.59, 0.15021, 99, -9.38, -9.59, 0.04173, 6, 94, 11.84, -9.52, 0.09592, 95, 5.55, -9.51, 0.39261, 96, -0.09, -9.51, 0.38335, 97, -5.08, -9.43, 0.11978, 98, -10.01, -9.59, 0.00806, 99, -15.67, -9.59, 0.00029, 4, 94, 5.55, -9.6, 0.57181, 95, -0.75, -9.6, 0.33213, 96, -6.38, -9.51, 0.09086, 97, -11.37, -9.32, 0.0052, 3, 94, -0.74, -9.68, 0.89743, 95, -7.04, -9.69, 0.10057, 96, -12.67, -9.51, 0.002], "hull": 50, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 0], "width": 151, "height": 17}}, "light2": {"light0": {"type": "mesh", "uvs": [1, 1, 0.95833, 1, 0.91667, 1, 0.875, 1, 0.83333, 1, 0.79167, 1, 0.75, 1, 0.70833, 1, 0.66667, 1, 0.625, 1, 0.58333, 1, 0.54167, 1, 0.5, 1, 0.45833, 1, 0.41667, 1, 0.375, 1, 0.33333, 1, 0.29167, 1, 0.25, 1, 0.20833, 1, 0.16667, 1, 0.125, 1, 0.08333, 1, 0.04167, 1, 0, 1, 0, 0, 0.04167, 0, 0.08333, 0, 0.125, 0, 0.16667, 0, 0.20833, 0, 0.25, 0, 0.29167, 0, 0.33333, 0, 0.375, 0, 0.41667, 0, 0.45833, 0, 0.5, 0, 0.54167, 0, 0.58333, 0, 0.625, 0, 0.66667, 0, 0.70833, 0, 0.75, 0, 0.79167, 0, 0.83333, 0, 0.875, 0, 0.91667, 0, 0.95833, 0, 1, 0], "triangles": [24, 26, 25, 23, 26, 24, 23, 27, 26, 22, 27, 23, 22, 28, 27, 21, 28, 22, 21, 29, 28, 20, 29, 21, 20, 30, 29, 19, 30, 20, 19, 31, 30, 18, 31, 19, 18, 32, 31, 17, 32, 18, 17, 33, 32, 16, 33, 17, 16, 34, 33, 15, 34, 16, 15, 35, 34, 14, 35, 15, 14, 36, 35, 13, 36, 14, 13, 37, 36, 12, 37, 13, 12, 38, 37, 11, 38, 12, 11, 39, 38, 10, 39, 11, 10, 40, 39, 9, 40, 10, 9, 41, 40, 8, 41, 9, 8, 42, 41, 7, 42, 8, 7, 43, 42, 6, 43, 7, 6, 44, 43, 5, 44, 6, 5, 45, 44, 4, 45, 5, 4, 46, 45, 3, 46, 4, 3, 47, 46, 2, 47, 3, 2, 48, 47, 1, 48, 2, 1, 49, 48, 0, 49, 1], "vertices": [2, 118, -1.53, 7.1, 0.98451, 119, -7.86, 7.08, 0.01549, 3, 118, 4.76, 7.18, 0.65195, 119, -1.57, 7.18, 0.30235, 120, -6.95, 7.28, 0.0457, 5, 118, 11.05, 7.26, 0.07894, 119, 4.72, 7.27, 0.46443, 120, -0.66, 7.28, 0.39931, 121, -5.38, 7.37, 0.05601, 122, -10.59, 7.2, 0.00132, 4, 119, 11.01, 7.36, 0.06578, 120, 5.63, 7.28, 0.35609, 121, 0.91, 7.27, 0.43841, 122, -4.29, 7.2, 0.13973, 5, 120, 11.92, 7.28, 0.02274, 121, 7.2, 7.17, 0.2609, 122, 2, 7.2, 0.57179, 123, -3.66, 7.2, 0.14224, 124, -9.41, 7.07, 0.00233, 5, 121, 13.49, 7.06, 0.01614, 122, 8.29, 7.2, 0.20735, 123, 2.63, 7.2, 0.5955, 124, -3.12, 7.16, 0.17447, 125, -9.09, 7.28, 0.00655, 5, 122, 14.58, 7.2, 0.00201, 123, 8.92, 7.2, 0.20803, 124, 3.17, 7.24, 0.55693, 125, -2.8, 7.28, 0.22679, 126, -8.46, 7.28, 0.00625, 5, 123, 15.22, 7.2, 0.0059, 124, 9.46, 7.33, 0.14744, 125, 3.49, 7.28, 0.58228, 126, -2.17, 7.28, 0.25204, 127, -7.74, 7.28, 0.01234, 5, 124, 15.75, 7.41, 0.00089, 125, 9.78, 7.28, 0.11449, 126, 4.12, 7.28, 0.55506, 127, -1.45, 7.28, 0.31089, 128, -7.94, 7.18, 0.01867, 4, 126, 10.41, 7.28, 0.10352, 127, 4.84, 7.28, 0.57061, 128, -1.65, 7.26, 0.31749, 129, -8.29, 7.26, 0.00838, 5, 126, 16.71, 7.28, 0.00012, 127, 11.13, 7.28, 0.11018, 128, 4.64, 7.34, 0.59503, 129, -2, 7.34, 0.28679, 130, -8.71, 7.44, 0.00788, 4, 128, 10.94, 7.42, 0.1169, 129, 4.3, 7.42, 0.61401, 130, -2.42, 7.44, 0.26379, 131, -9.06, 7.44, 0.0053, 4, 129, 10.59, 7.49, 0.13532, 130, 3.87, 7.44, 0.63761, 131, -2.77, 7.44, 0.21896, 132, -9.33, 7.44, 0.00811, 4, 129, 16.88, 7.57, 0.00025, 130, 10.16, 7.44, 0.1517, 131, 3.52, 7.44, 0.60997, 132, -3.04, 7.44, 0.23807, 5, 130, 16.45, 7.44, 0.00127, 131, 9.81, 7.44, 0.1691, 132, 3.25, 7.44, 0.7748, 133, -3.36, 7.48, 0.04898, 134, -9.3, 7.45, 0.00585, 5, 131, 16.1, 7.44, 0.00192, 132, 9.54, 7.44, 0.27549, 133, 2.93, 7.48, 0.4998, 134, -3, 7.53, 0.21703, 135, -9.23, 7.64, 0.00576, 5, 132, 15.83, 7.44, 0.00652, 133, 9.22, 7.48, 0.16829, 134, 3.29, 7.61, 0.60441, 135, -2.94, 7.64, 0.20813, 136, -8.73, 7.52, 0.01265, 5, 133, 15.51, 7.48, 0.00295, 134, 9.58, 7.69, 0.19333, 135, 3.35, 7.64, 0.53592, 136, -2.44, 7.61, 0.25724, 137, -7.85, 7.72, 0.01055, 5, 134, 15.87, 7.77, 0.00453, 135, 9.65, 7.64, 0.15615, 136, 3.85, 7.7, 0.52787, 137, -1.55, 7.72, 0.28472, 138, -7.55, 7.72, 0.02673, 5, 135, 15.94, 7.64, 0.00379, 136, 10.15, 7.79, 0.11811, 137, 4.74, 7.72, 0.49772, 138, -1.26, 7.72, 0.35408, 139, -7.58, 7.72, 0.02629, 5, 136, 16.44, 7.88, 0.00104, 137, 11.03, 7.72, 0.09244, 138, 5.03, 7.72, 0.5141, 139, -1.29, 7.72, 0.34729, 140, -7.17, 7.62, 0.04514, 5, 137, 17.32, 7.72, 0.00022, 138, 11.32, 7.72, 0.07945, 139, 5, 7.72, 0.46753, 140, -0.88, 7.71, 0.41618, 141, -6.18, 7.63, 0.03663, 3, 139, 11.29, 7.72, 0.05895, 140, 5.41, 7.81, 0.4576, 141, 0.11, 7.81, 0.48344, 2, 140, 11.7, 7.91, 0.06835, 141, 6.4, 7.99, 0.93165, 1, 141, 12.69, 8.17, 1, 3, 139, 23.88, -9.28, 0, 140, 18.26, -9, 0.00058, 141, 13.17, -8.83, 0.99942, 3, 139, 17.58, -9.28, 0.00169, 140, 11.97, -9.09, 0.08275, 141, 6.88, -9.01, 0.91556, 4, 138, 17.61, -9.28, 0.01228, 139, 11.29, -9.28, 0.09041, 140, 5.67, -9.19, 0.40126, 141, 0.59, -9.18, 0.49605, 5, 137, 17.32, -9.28, 0.00143, 138, 11.32, -9.28, 0.17045, 139, 5, -9.28, 0.37827, 140, -0.62, -9.29, 0.38457, 141, -5.7, -9.36, 0.06527, 5, 136, 16.68, -9.11, 0.00706, 137, 11.03, -9.28, 0.08856, 138, 5.03, -9.28, 0.5491, 139, -1.29, -9.28, 0.28139, 140, -6.91, -9.38, 0.07389, 6, 135, 15.94, -9.36, 0.01324, 136, 10.39, -9.21, 0.14409, 137, 4.74, -9.28, 0.41493, 138, -1.26, -9.28, 0.38975, 139, -7.58, -9.28, 0.03635, 140, -13.2, -9.48, 0.00164, 5, 134, 16.08, -9.23, 0.01746, 135, 9.65, -9.36, 0.17079, 136, 4.1, -9.3, 0.46946, 137, -1.55, -9.28, 0.28311, 138, -7.55, -9.28, 0.05918, 6, 133, 15.51, -9.52, 0.018, 134, 9.79, -9.31, 0.22281, 135, 3.35, -9.36, 0.44369, 136, -2.19, -9.39, 0.28036, 137, -7.85, -9.28, 0.03479, 138, -13.85, -9.28, 0.00036, 5, 132, 15.83, -9.56, 0.01891, 133, 9.22, -9.52, 0.21142, 134, 3.5, -9.39, 0.52947, 135, -2.94, -9.36, 0.20453, 136, -8.48, -9.48, 0.03567, 6, 131, 16.1, -9.56, 0.02085, 132, 9.54, -9.56, 0.2164, 133, 2.93, -9.52, 0.49637, 134, -2.79, -9.47, 0.24659, 135, -9.23, -9.36, 0.01979, 136, -14.77, -9.57, 1e-05, 5, 130, 16.45, -9.56, 0.0137, 131, 9.81, -9.56, 0.22819, 132, 3.25, -9.56, 0.50944, 133, -3.36, -9.52, 0.22555, 134, -9.08, -9.55, 0.02312, 5, 129, 17.08, -9.43, 0.00774, 130, 10.16, -9.56, 0.2041, 131, 3.52, -9.56, 0.55437, 132, -3.04, -9.56, 0.21191, 133, -9.65, -9.52, 0.02189, 5, 128, 17.44, -9.5, 0.01049, 129, 10.79, -9.51, 0.1668, 130, 3.87, -9.56, 0.54071, 131, -2.77, -9.56, 0.26785, 132, -9.33, -9.56, 0.01415, 5, 127, 17.42, -9.72, 0.00926, 128, 11.15, -9.58, 0.15789, 129, 4.5, -9.58, 0.52544, 130, -2.42, -9.56, 0.27685, 131, -9.06, -9.56, 0.03056, 5, 126, 16.71, -9.72, 0.00833, 127, 11.13, -9.72, 0.16143, 128, 4.85, -9.66, 0.47669, 129, -1.79, -9.66, 0.32431, 130, -8.71, -9.56, 0.02924, 5, 125, 16.07, -9.72, 0.01645, 126, 10.41, -9.72, 0.13531, 127, 4.84, -9.72, 0.49694, 128, -1.44, -9.74, 0.30666, 129, -8.08, -9.74, 0.04465, 6, 124, 15.98, -9.59, 0.01296, 125, 9.78, -9.72, 0.18605, 126, 4.12, -9.72, 0.43205, 127, -1.45, -9.72, 0.32768, 128, -7.73, -9.82, 0.04108, 129, -14.37, -9.81, 0.00017, 6, 122, 20.87, -9.8, 0.00073, 123, 15.22, -9.8, 0.01383, 124, 9.69, -9.67, 0.18029, 125, 3.49, -9.72, 0.48982, 126, -2.17, -9.72, 0.26613, 127, -7.74, -9.72, 0.0492, 7, 121, 19.51, -10.04, 0.00028, 122, 14.58, -9.8, 0.04704, 123, 8.92, -9.8, 0.17899, 124, 3.4, -9.76, 0.47734, 125, -2.8, -9.72, 0.2583, 126, -8.46, -9.72, 0.03799, 127, -14.04, -9.72, 5e-05, 6, 120, 18.21, -9.72, 0.00259, 121, 13.22, -9.93, 0.04435, 122, 8.29, -9.8, 0.27022, 123, 2.63, -9.8, 0.39323, 124, -2.89, -9.84, 0.26454, 125, -9.09, -9.72, 0.02506, 6, 119, 17.56, -9.54, 0.00129, 120, 11.92, -9.72, 0.07054, 121, 6.92, -9.83, 0.29244, 122, 2, -9.8, 0.42835, 123, -3.66, -9.8, 0.1713, 124, -9.18, -9.93, 0.03609, 7, 118, 17.56, -9.66, 0.00911, 119, 11.27, -9.63, 0.07887, 120, 5.63, -9.72, 0.34322, 121, 0.63, -9.73, 0.42022, 122, -4.29, -9.8, 0.12921, 123, -9.95, -9.8, 0.01918, 124, -15.48, -10.01, 0.0002, 5, 118, 11.27, -9.74, 0.16807, 119, 4.98, -9.73, 0.34376, 120, -0.66, -9.72, 0.3579, 121, -5.66, -9.63, 0.12498, 122, -10.59, -9.8, 0.00529, 4, 118, 4.98, -9.82, 0.63771, 119, -1.31, -9.82, 0.27911, 120, -6.95, -9.72, 0.07619, 121, -11.95, -9.53, 0.00699, 3, 118, -1.31, -9.9, 0.90089, 119, -7.61, -9.92, 0.0978, 120, -13.25, -9.72, 0.00131], "hull": 50, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 0], "width": 151, "height": 17}}, "lightsaochoi": {"lightsaochoi": {"x": 6.93, "y": 2.6, "rotation": -90, "width": 118, "height": 109}}, "logonho1": {"logonho1": {"x": -2.99, "y": -1.54, "width": 131, "height": 106}}, "logonho2": {"logonho2": {"x": -4.84, "y": 1.37, "width": 87, "height": 92}}, "logonho3": {"logonho3": {"x": 2.06, "y": 7.48, "width": 133, "height": 94}}, "logonho4": {"logonho4": {"type": "mesh", "uvs": [0, 0.21631, 0.07085, 0.15798, 0.155, 0.11607, 0.24636, 0.08508, 0.34253, 0.05228, 0.44351, 0.03223, 0.54449, 0.01947, 0.96283, 1, 0.87147, 1, 0.76258, 0.99999, 0.65921, 0.99998, 0.54336, 0.99998, 0.43581, 0.99999, 0.33532, 1], "triangles": [5, 8, 9, 9, 4, 5, 10, 4, 9, 3, 4, 10, 11, 3, 10, 2, 3, 11, 1, 13, 0, 1, 2, 12, 5, 6, 8, 11, 12, 2, 12, 13, 1, 6, 7, 8], "vertices": [1, 61, 30.47, 15.49, 1, 1, 61, 32.91, 11.22, 1, 1, 61, 34.19, 6.66, 1, 1, 61, 34.72, 1.99, 1, 1, 61, 35.29, -2.93, 1, 1, 61, 35.04, -7.83, 1, 1, 61, 34.36, -12.6, 1, 1, 61, -29.53, -12.95, 1, 1, 61, -28.23, -8.86, 1, 1, 61, -26.69, -3.98, 1, 1, 61, -25.22, 0.65, 1, 1, 61, -23.57, 5.84, 1, 1, 61, -22.04, 10.66, 1, 1, 61, -20.61, 15.16, 1], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 0, 26, 10, 16, 8, 18, 2, 24, 6, 20, 4, 22, 22, 24, 20, 22], "width": 47, "height": 62}}, "logonho5": {"logonho2": {"x": -4.84, "y": 1.37, "width": 87, "height": 92}}, "logonho6": {"logonho1": {"x": -2.99, "y": -1.54, "width": 131, "height": 106}}, "lvlup0": {"lvlup0": {"x": -1.27, "y": -0.13, "rotation": -90, "width": 97, "height": 90}, "lvlup1": {"x": -1.27, "y": -0.13, "rotation": -90, "width": 97, "height": 90}, "lvlup2": {"x": -1.27, "y": -0.13, "rotation": -90, "width": 97, "height": 90}, "lvlup3": {"x": -1.27, "y": -0.13, "rotation": -90, "width": 97, "height": 90}, "lvlup4": {"x": -1.27, "y": -0.13, "rotation": -90, "width": 97, "height": 90}, "lvlup5": {"x": -1.27, "y": -0.13, "rotation": -90, "width": 97, "height": 90}, "lvlup6": {"x": -1.27, "y": -0.13, "rotation": -90, "width": 97, "height": 90}, "lvlup7": {"x": -1.27, "y": -0.13, "rotation": -90, "width": 97, "height": 90}, "lvlup8": {"x": -1.27, "y": -0.13, "rotation": -90, "width": 97, "height": 90}}, "moi ban2": {"moi ban2": {"width": 30, "height": 30}}, "moi ban3": {"moi ban3": {"x": -0.25, "y": 0.71, "rotation": -90, "width": 43, "height": 43}}, "moiban": {"moiban": {"type": "path", "closed": true, "lengths": [12.54, 396.18, 410.65, 436.71, 451.18, 833.82, 846.76, 870.44], "vertexCount": 24, "vertices": [-200.33, 9.2, -200.15, 13.67, -200, 17.27, -195.52, 22.8, -192.55, 22.83, -133.31, 23.29, 131.57, 23.02, 191.09, 22.99, 193.85, 22.99, 200.44, 19.6, 200.82, 13.65, 201.3, 6.28, 201.02, -6.34, 201.12, -12.41, 201.21, -18.27, 195.84, -21.93, 192, -21.98, 132.55, -22.68, -128.42, -21.92, -190.64, -22.44, -194.41, -22.47, -199.66, -18.11, -199.79, -14.47, -199.97, -9.29]}}, "moiban2": {"moiban": {"type": "path", "closed": true, "lengths": [12.54, 396.18, 410.65, 436.71, 451.18, 833.82, 846.76, 870.44], "vertexCount": 24, "vertices": [-200.33, 9.2, -200.15, 13.67, -200, 17.27, -195.52, 22.8, -192.55, 22.83, -133.31, 23.29, 131.57, 23.02, 191.09, 22.99, 193.85, 22.99, 200.44, 19.6, 200.82, 13.65, 201.3, 6.28, 201.02, -6.34, 201.12, -12.41, 201.21, -18.27, 195.84, -21.93, 192, -21.98, 132.55, -22.68, -128.42, -21.92, -190.64, -22.44, -194.41, -22.47, -199.66, -18.11, -199.79, -14.47, -199.97, -9.29]}}, "option301": {"option301": {"width": 124, "height": 123}}, "option302": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option303": {"option303": {"x": 4.06, "y": -2.03, "width": 105, "height": 88}}, "option304": {"option304": {"x": -22.94, "y": 9.39, "width": 45, "height": 61}, "option305": {"x": -17.91, "y": 5.41, "width": 55, "height": 69}, "option306": {"x": -12.45, "y": 3.39, "width": 66, "height": 73}, "option307": {"x": -8.42, "y": 2.45, "width": 74, "height": 75}, "option308": {"x": -3.44, "y": 0.5, "width": 82, "height": 79}, "option309": {"x": -0.39, "width": 76, "height": 78}, "option310": {"x": 15.08, "y": -3.03, "width": 75, "height": 72}, "option311": {"x": 22.51, "y": -8.07, "width": 60, "height": 62}, "option312": {"x": 32.05, "y": -20.6, "width": 41, "height": 35}}, "option305": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option306": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option307": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option308": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option309": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option310": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option311": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option312": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option313": {"option313": {"x": 4.67, "y": -19.61, "width": 102, "height": 39}}, "option314": {"option314": {"x": -4.25, "y": 18.76, "width": 90, "height": 49}}, "option315": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option316": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option317": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option318": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option319": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option320": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option321": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option322": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option323": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option324": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option325": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option326": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option327": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option328": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option329": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option330": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option331": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option332": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option333": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option334": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option335": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option336": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option337": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option338": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option339": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option340": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "option341": {"option302": {"x": -0.13, "y": 0.01, "rotation": -90, "width": 11, "height": 10}}, "path": {"path": {"type": "path", "closed": true, "lengths": [218.76, 296.49, 516.75, 584.95], "vertexCount": 12, "vertices": [-144.61, 25.59, -108.72, 25.78, -41.35, 26.16, 65.85, 25.93, 110.04, 25.53, 144.32, 25.22, 142.47, -25.99, 111.07, -25.94, 68.84, -25.87, -41.82, -25.62, -109.19, -26.12, -141.9, -26.36]}}, "ruttien0": {"ruttien0": {"type": "mesh", "uvs": [1, 1, 0.71877, 1, 0.28189, 1, 0, 1, 0, 0, 0.25401, 0, 1, 0, 0.60668, 0.29072, 0.77857, 0.04541, 0.72174, 0.97063, 0.61643, 0.91329, 0.85853, 0.11432, 0.81437, 0.96221, 0.62479, 0.96347, 0.61086, 0.23584], "triangles": [8, 5, 6, 11, 8, 6, 14, 5, 8, 7, 5, 14, 7, 14, 8, 10, 7, 9, 8, 9, 7, 9, 8, 11, 9, 13, 10, 9, 11, 12, 3, 4, 5, 2, 3, 5, 7, 2, 5, 10, 2, 7, 2, 10, 13, 1, 13, 9, 2, 13, 1, 11, 6, 0, 12, 11, 0, 1, 9, 12, 0, 1, 12], "vertices": [49.5, -44, 21.66, -44, -21.59, -44, -49.5, -44, -49.5, 44, -24.35, 44, 49.5, 44, 10.56, 18.42, 27.58, 40, 21.95, -41.42, 11.53, -36.37, 35.49, 33.94, 31.12, -40.67, 12.35, -40.79, 10.97, 23.25], "hull": 7, "edges": [6, 8, 0, 12, 14, 16, 16, 18, 18, 20, 20, 14, 16, 22, 22, 24, 24, 18, 4, 6, 26, 4, 8, 10, 10, 12, 4, 10, 10, 28, 0, 2, 2, 4, 26, 2], "width": 99, "height": 88}}, "ruttien1": {"ruttien1": {"x": -32.03, "y": 0.04, "width": 35, "height": 88}}, "ruttien2": {"ruttien2": {"x": 1.09, "y": -2.68, "rotation": -75.51, "width": 60, "height": 62}}, "ruttien3": {"ruttien2": {"x": 1.09, "y": -2.68, "rotation": -75.51, "width": 60, "height": 62}}, "ruttien4": {"ruttien2": {"x": 1.09, "y": -2.68, "rotation": -75.51, "width": 60, "height": 62}}, "star": {"star": {"x": 0.12, "y": -0.01, "rotation": -90.91, "width": 41, "height": 41}}, "star2": {"star": {"x": 0.12, "y": -0.01, "rotation": -90.91, "width": 41, "height": 41}}, "star3": {"star": {"x": -0.01, "y": -0.02, "rotation": -90, "width": 41, "height": 41}}, "tai": {"tai": {"x": 4.25, "y": 5.28, "rotation": -90, "width": 95, "height": 64}}, "tai2": {"tai": {"x": 4.25, "y": 5.28, "rotation": -90, "width": 95, "height": 64}}, "thanhmoiban": {"thanhmoiban": {"width": 403, "height": 47}}, "xiu": {"xiu": {"x": 5.34, "y": 2.58, "rotation": -90, "width": 101, "height": 65}}, "xiu2": {"xiu": {"x": 5.34, "y": 2.58, "rotation": -90, "width": 101, "height": 65}}}}], "animations": {"Jackpot_icon": {"slots": {"Jackpot Icon1": {"attachment": [{"time": 0, "name": "Jackpot Icon1"}]}, "Jackpot Icon3": {"attachment": [{"time": 0, "name": "Jackpot Icon3"}]}, "Jackpot Icon4": {"attachment": [{"time": 0, "name": "Jackpot Icon4"}]}, "Jackpot Icon5": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Jackpot Icon5"}]}, "Jackpot Icon6": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2, "color": "ffffff00"}, {"time": 1.2333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Jackpot Icon6"}]}, "Jackpot Icon7": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6333, "color": "ffffff00"}, {"time": 1.7, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Jackpot Icon7"}]}, "Jackpot Icon8": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 1.8667, "color": "ffffff00"}, {"time": 1.9333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Jackpot Icon8"}]}, "Jackpot Icon9": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.9667, "color": "ffffff00"}, {"time": 2.0333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Jackpot Icon9"}]}, "Jackpot Icon10": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1667, "color": "ffffff00"}, {"time": 2.2333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Jackpot Icon10"}]}, "Jackpot Icon11": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 2.4667, "color": "ffffff00"}, {"time": 2.5333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Jackpot Icon11"}]}, "Jackpot Icon border": {"attachment": [{"time": 0, "name": "Jackpot Icon border"}]}, "Jackpot roll0": {"attachment": [{"time": 0, "name": "Jackpot roll12"}, {"time": 0.0667, "name": "Jackpot roll11"}, {"time": 0.1333, "name": "Jackpot roll10"}, {"time": 0.2, "name": "Jackpot roll9"}, {"time": 0.2667, "name": "Jackpot roll8"}, {"time": 0.3333, "name": "Jackpot roll7"}, {"time": 0.4, "name": "Jackpot roll6"}, {"time": 0.4667, "name": "Jackpot roll5"}, {"time": 0.5333, "name": "Jackpot roll4"}, {"time": 0.6, "name": "Jackpot roll3"}, {"time": 0.6667, "name": "Jackpot roll2"}, {"time": 0.7333, "name": "Jackpot roll1"}, {"time": 0.8, "name": "Jackpot roll0"}, {"time": 0.8667, "name": "Jackpot roll14"}, {"time": 0.9333, "name": null}]}, "Jackpot roll1": {"attachment": [{"time": 0, "name": "Jackpot roll0"}, {"time": 0.0667, "name": "Jackpot roll14"}, {"time": 0.1333, "name": "Jackpot roll13"}, {"time": 0.2, "name": "Jackpot roll12"}, {"time": 0.2667, "name": "Jackpot roll11"}, {"time": 0.3333, "name": "Jackpot roll10"}, {"time": 0.4, "name": "Jackpot roll9"}, {"time": 0.4667, "name": "Jackpot roll8"}, {"time": 0.5333, "name": "Jackpot roll7"}, {"time": 0.6, "name": "Jackpot roll6"}, {"time": 0.6667, "name": "Jackpot roll5"}, {"time": 0.7333, "name": "Jackpot roll4"}, {"time": 0.8, "name": "Jackpot roll3"}, {"time": 0.8667, "name": "Jackpot roll2"}, {"time": 0.9333, "name": "Jackpot roll1"}, {"time": 1, "name": "Jackpot roll0"}, {"time": 1.0667, "name": "Jackpot roll14"}, {"time": 1.1333, "name": "Jackpot roll13"}, {"time": 1.2, "name": "Jackpot roll12"}, {"time": 1.2667, "name": null}]}, "Jackpot roll2": {"attachment": [{"time": 0, "name": "Jackpot roll11"}, {"time": 0.0667, "name": "Jackpot roll12"}, {"time": 0.1333, "name": "Jackpot roll13"}, {"time": 0.2, "name": "Jackpot roll14"}, {"time": 0.2667, "name": "Jackpot roll0"}, {"time": 0.3333, "name": "Jackpot roll14"}, {"time": 0.4, "name": "Jackpot roll13"}, {"time": 0.4667, "name": "Jackpot roll12"}, {"time": 0.5333, "name": "Jackpot roll11"}, {"time": 0.6, "name": "Jackpot roll10"}, {"time": 0.6667, "name": "Jackpot roll9"}, {"time": 0.7333, "name": "Jackpot roll8"}, {"time": 0.8, "name": "Jackpot roll7"}, {"time": 0.8667, "name": "Jackpot roll6"}, {"time": 0.9333, "name": "Jackpot roll5"}, {"time": 1, "name": "Jackpot roll4"}, {"time": 1.0667, "name": "Jackpot roll3"}, {"time": 1.1333, "name": "Jackpot roll2"}, {"time": 1.2, "name": "Jackpot roll1"}, {"time": 1.2667, "name": "Jackpot roll0"}, {"time": 1.3333, "name": "Jackpot roll14"}, {"time": 1.4, "name": "Jackpot roll13"}, {"time": 1.4667, "name": "Jackpot roll12"}, {"time": 1.5333, "name": "Jackpot roll11"}, {"time": 1.6, "name": "Jackpot roll10"}, {"time": 1.6667, "name": "Jackpot roll9"}, {"time": 1.7, "name": null}]}, "Jackpot roll3": {"attachment": [{"time": 0, "name": "Jackpot roll13"}, {"time": 0.0667, "name": "Jackpot roll14"}, {"time": 0.1333, "name": "Jackpot roll0"}, {"time": 0.2, "name": "Jackpot roll1"}, {"time": 0.2667, "name": "Jackpot roll2"}, {"time": 0.3333, "name": "Jackpot roll1"}, {"time": 0.4, "name": "Jackpot roll0"}, {"time": 0.4667, "name": "Jackpot roll14"}, {"time": 0.5333, "name": "Jackpot roll13"}, {"time": 0.6, "name": "Jackpot roll12"}, {"time": 0.6667, "name": "Jackpot roll11"}, {"time": 0.7333, "name": "Jackpot roll10"}, {"time": 0.8, "name": "Jackpot roll9"}, {"time": 0.8667, "name": "Jackpot roll8"}, {"time": 0.9333, "name": "Jackpot roll7"}, {"time": 1, "name": "Jackpot roll6"}, {"time": 1.0667, "name": "Jackpot roll5"}, {"time": 1.1333, "name": "Jackpot roll4"}, {"time": 1.2, "name": "Jackpot roll3"}, {"time": 1.2667, "name": "Jackpot roll2"}, {"time": 1.3333, "name": "Jackpot roll1"}, {"time": 1.4, "name": "Jackpot roll0"}, {"time": 1.4667, "name": "Jackpot roll14"}, {"time": 1.5333, "name": "Jackpot roll13"}, {"time": 1.6, "name": "Jackpot roll12"}, {"time": 1.6667, "name": "Jackpot roll11"}, {"time": 1.7333, "name": "Jackpot roll10"}, {"time": 1.8, "name": "Jackpot roll9"}, {"time": 1.8667, "name": "Jackpot roll8"}, {"time": 1.9, "name": null}]}, "Jackpot roll4": {"attachment": [{"time": 0, "name": "Jackpot roll10"}, {"time": 0.0667, "name": "Jackpot roll11"}, {"time": 0.1333, "name": "Jackpot roll12"}, {"time": 0.2, "name": "Jackpot roll13"}, {"time": 0.2667, "name": "Jackpot roll14"}, {"time": 0.3333, "name": "Jackpot roll0"}, {"time": 0.4, "name": "Jackpot roll14"}, {"time": 0.4667, "name": "Jackpot roll13"}, {"time": 0.5333, "name": "Jackpot roll12"}, {"time": 0.6, "name": "Jackpot roll11"}, {"time": 0.6667, "name": "Jackpot roll10"}, {"time": 0.7333, "name": "Jackpot roll9"}, {"time": 0.8, "name": "Jackpot roll8"}, {"time": 0.8667, "name": "Jackpot roll7"}, {"time": 0.9333, "name": "Jackpot roll6"}, {"time": 1, "name": "Jackpot roll5"}, {"time": 1.0667, "name": "Jackpot roll4"}, {"time": 1.1333, "name": "Jackpot roll3"}, {"time": 1.2, "name": "Jackpot roll2"}, {"time": 1.2667, "name": "Jackpot roll1"}, {"time": 1.3333, "name": "Jackpot roll0"}, {"time": 1.4, "name": "Jackpot roll14"}, {"time": 1.4667, "name": "Jackpot roll13"}, {"time": 1.5333, "name": "Jackpot roll12"}, {"time": 1.6, "name": "Jackpot roll11"}, {"time": 1.6667, "name": "Jackpot roll10"}, {"time": 1.7333, "name": "Jackpot roll9"}, {"time": 1.8, "name": "Jackpot roll8"}, {"time": 1.8667, "name": "Jackpot roll7"}, {"time": 1.9333, "name": "Jackpot roll6"}, {"time": 2, "name": "Jackpot roll5"}, {"time": 2.0333, "name": null}]}, "Jackpot roll5": {"attachment": [{"time": 0, "name": "Jackpot roll6"}, {"time": 0.0667, "name": "Jackpot roll5"}, {"time": 0.1333, "name": "Jackpot roll4"}, {"time": 0.2, "name": "Jackpot roll3"}, {"time": 0.2667, "name": "Jackpot roll2"}, {"time": 0.3333, "name": "Jackpot roll1"}, {"time": 0.4, "name": "Jackpot roll0"}, {"time": 0.4667, "name": "Jackpot roll14"}, {"time": 0.5333, "name": "Jackpot roll13"}, {"time": 0.6, "name": "Jackpot roll12"}, {"time": 0.6667, "name": "Jackpot roll11"}, {"time": 0.7333, "name": "Jackpot roll10"}, {"time": 0.8, "name": "Jackpot roll9"}, {"time": 0.8667, "name": "Jackpot roll8"}, {"time": 0.9333, "name": "Jackpot roll7"}, {"time": 1, "name": "Jackpot roll6"}, {"time": 1.0667, "name": "Jackpot roll5"}, {"time": 1.1333, "name": "Jackpot roll4"}, {"time": 1.2, "name": "Jackpot roll3"}, {"time": 1.2667, "name": "Jackpot roll2"}, {"time": 1.3333, "name": "Jackpot roll1"}, {"time": 1.4, "name": "Jackpot roll0"}, {"time": 1.4667, "name": "Jackpot roll14"}, {"time": 1.5333, "name": "Jackpot roll13"}, {"time": 1.6, "name": "Jackpot roll12"}, {"time": 1.6667, "name": "Jackpot roll11"}, {"time": 1.7333, "name": "Jackpot roll10"}, {"time": 1.8, "name": "Jackpot roll9"}, {"time": 1.8667, "name": "Jackpot roll8"}, {"time": 1.9333, "name": "Jackpot roll7"}, {"time": 2, "name": "Jackpot roll6"}, {"time": 2.0667, "name": "Jackpot roll5"}, {"time": 2.1333, "name": "Jackpot roll4"}, {"time": 2.2, "name": "Jackpot roll3"}, {"time": 2.2333, "name": null}]}, "Jackpot roll6": {"attachment": [{"time": 0, "name": "Jackpot roll8"}, {"time": 0.0667, "name": "Jackpot roll7"}, {"time": 0.1333, "name": "Jackpot roll6"}, {"time": 0.2, "name": "Jackpot roll5"}, {"time": 0.2667, "name": "Jackpot roll4"}, {"time": 0.3333, "name": "Jackpot roll3"}, {"time": 0.4, "name": "Jackpot roll2"}, {"time": 0.4667, "name": "Jackpot roll1"}, {"time": 0.5333, "name": "Jackpot roll0"}, {"time": 0.6, "name": "Jackpot roll14"}, {"time": 0.6667, "name": "Jackpot roll13"}, {"time": 0.7333, "name": "Jackpot roll12"}, {"time": 0.8, "name": "Jackpot roll11"}, {"time": 0.8667, "name": "Jackpot roll10"}, {"time": 0.9333, "name": "Jackpot roll9"}, {"time": 1, "name": "Jackpot roll8"}, {"time": 1.0667, "name": "Jackpot roll7"}, {"time": 1.1333, "name": "Jackpot roll6"}, {"time": 1.2, "name": "Jackpot roll5"}, {"time": 1.2667, "name": "Jackpot roll4"}, {"time": 1.3333, "name": "Jackpot roll3"}, {"time": 1.4, "name": "Jackpot roll2"}, {"time": 1.4667, "name": "Jackpot roll1"}, {"time": 1.5333, "name": "Jackpot roll0"}, {"time": 1.6, "name": "Jackpot roll14"}, {"time": 1.6667, "name": "Jackpot roll13"}, {"time": 1.7333, "name": "Jackpot roll12"}, {"time": 1.8, "name": "Jackpot roll11"}, {"time": 1.8667, "name": "Jackpot roll10"}, {"time": 1.9333, "name": "Jackpot roll9"}, {"time": 2, "name": "Jackpot roll8"}, {"time": 2.0667, "name": "Jackpot roll7"}, {"time": 2.1333, "name": "Jackpot roll6"}, {"time": 2.2, "name": "Jackpot roll5"}, {"time": 2.2667, "name": "Jackpot roll4"}, {"time": 2.3333, "name": "Jackpot roll3"}, {"time": 2.4, "name": "Jackpot roll2"}, {"time": 2.4667, "name": "Jackpot roll1"}, {"time": 2.5333, "name": null}]}, "Nap tien34": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0667, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0667, "color": "ffffffff"}, {"time": 2.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.5, "color": "ffffff00"}, {"time": 2.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.0667, "color": "ffffffff"}, {"time": 3.1667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Nap tien5"}]}, "Nap tien35": {"color": [{"time": 0.1667, "color": "ffffff00"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1667, "color": "ffffff00"}, {"time": 1.2, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7333, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1667, "color": "ffffff00"}, {"time": 2.2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7333, "color": "ffffffff"}, {"time": 2.8333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Nap tien5"}]}, "Nap tien36": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 0.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7333, "color": "ffffff00"}, {"time": 1.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3, "color": "ffffffff"}, {"time": 2.4, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6667, "color": "ffffff00"}, {"time": 2.7, "color": "ffffffff", "curve": "stepped"}, {"time": 3.2333, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Nap tien5"}]}, "backlight": {"attachment": [{"time": 0, "name": "backlight"}]}, "bongden0": {"attachment": [{"time": 0, "name": null}]}}, "bones": {"bone44": {"translate": [{"time": 0.9667, "x": -1.63, "y": 0}, {"time": 1, "x": -6.05, "y": 0}, {"time": 1.0667, "x": -7.79, "y": 0}, {"time": 1.1, "x": -6.05, "y": 0}], "scale": [{"time": 0.8667, "x": 1, "y": 1}, {"time": 0.9667, "x": 1.459, "y": 1}, {"time": 1, "x": 1.963, "y": 1}]}, "bone45": {"translate": [{"time": 1.3, "x": -6.18, "y": 0}, {"time": 1.3667, "x": -8.29, "y": 0}, {"time": 1.4, "x": -6.18, "y": 0}], "scale": [{"time": 1.2333, "x": 1, "y": 1}, {"time": 1.3, "x": 2.227, "y": 1}]}, "bone46": {"translate": [{"time": 1.6333, "x": 0, "y": 0}, {"time": 1.7, "x": -6.61, "y": 0}, {"time": 1.7667, "x": -8.69, "y": 0}, {"time": 1.8, "x": -6.61, "y": 0}], "scale": [{"time": 1.6333, "x": 1, "y": 1}, {"time": 1.7, "x": 2.337, "y": 1}]}, "bone47": {"translate": [{"time": 1.8667, "x": 0, "y": 0}, {"time": 1.9333, "x": -6.54, "y": 0}, {"time": 2, "x": -8.22, "y": 0}, {"time": 2.0333, "x": -6.54, "y": 0}], "scale": [{"time": 1.8667, "x": 1, "y": 1}, {"time": 1.9333, "x": 2.488, "y": 1}]}, "bone48": {"translate": [{"time": 1.9667, "x": 0, "y": 0}, {"time": 2.0333, "x": -6.83, "y": 0}, {"time": 2.1, "x": -8.32, "y": 0}, {"time": 2.1333, "x": -6.83, "y": 0}], "scale": [{"time": 1.9667, "x": 1, "y": 1}, {"time": 2.0333, "x": 2.432, "y": 1}]}, "bone49": {"translate": [{"time": 2.1667, "x": 0, "y": 0}, {"time": 2.2333, "x": -6.55, "y": 0}, {"time": 2.3, "x": -9.01, "y": 0}, {"time": 2.3333, "x": -6.55, "y": 0}], "scale": [{"time": 2.1667, "x": 1, "y": 1}, {"time": 2.2333, "x": 2.509, "y": 1}]}, "bone50": {"translate": [{"time": 2.4667, "x": 0, "y": 0}, {"time": 2.5333, "x": -6.21, "y": 0}, {"time": 2.6, "x": -8.02, "y": 0}, {"time": 2.6333, "x": -6.21, "y": 0}], "scale": [{"time": 2.4667, "x": 1, "y": 1}, {"time": 2.5333, "x": 2.52, "y": 1}]}, "bone57": {"scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.907, "y": 0.907}, {"time": 1.6667, "x": 1, "y": 1}, {"time": 2.5, "x": 0.913, "y": 0.913}, {"time": 3.3333, "x": 1, "y": 1}]}, "bone58": {"rotate": [{"time": 0.5, "angle": 31.56}, {"time": 1.1667, "angle": -62.87}, {"time": 1.5, "angle": 31.56}, {"time": 2.1667, "angle": -62.87}, {"time": 2.5, "angle": 31.56}, {"time": 3.1667, "angle": -62.87}], "translate": [{"time": 0.5, "x": 2.33, "y": -0.16}, {"time": 0.7, "x": 9.2, "y": 26.64}, {"time": 0.8, "x": 13.17, "y": 32.1}, {"time": 0.8667, "x": 17.18, "y": 27.94}, {"time": 1.1667, "x": 31.07, "y": -17.5}, {"time": 1.5, "x": 2.33, "y": -0.16}, {"time": 1.7333, "x": 8.75, "y": 31.29}, {"time": 1.8, "x": 10.58, "y": 37.48}, {"time": 1.9, "x": 11.81, "y": 28.38}, {"time": 2.1667, "x": 16.91, "y": -19.86}, {"time": 2.5, "x": 2.33, "y": -0.16}, {"time": 2.7, "x": 9.2, "y": 26.64}, {"time": 2.8, "x": 13.17, "y": 32.1}, {"time": 2.8667, "x": 17.18, "y": 27.94}, {"time": 3.1667, "x": 31.07, "y": -17.5}], "scale": [{"time": 0.5, "x": 1, "y": 1}, {"time": 1.1667, "x": 1.816, "y": 1.816}, {"time": 1.5, "x": 1, "y": 1}, {"time": 2.1667, "x": 1.816, "y": 1.816}, {"time": 2.5, "x": 1, "y": 1}, {"time": 3.1667, "x": 1.816, "y": 1.816}]}, "bone59": {"rotate": [{"time": 0.1667, "angle": 31.56}, {"time": 0.8333, "angle": 19.72}, {"time": 1.1667, "angle": 31.56}, {"time": 1.8333, "angle": 9.54}, {"time": 2.1667, "angle": 31.56}, {"time": 2.8333, "angle": -62.87}], "translate": [{"time": 0.1667, "x": 2.33, "y": -0.16}, {"time": 0.3667, "x": -9.62, "y": 31.85}, {"time": 0.4667, "x": -14.65, "y": 40.9}, {"time": 0.5667, "x": -22, "y": 32.88}, {"time": 0.8333, "x": -35.39, "y": -17.8}, {"time": 1.1667, "x": 2.33, "y": -0.16}, {"time": 1.4, "x": -1.52, "y": 29.97}, {"time": 1.4667, "x": -2.62, "y": 33.94}, {"time": 1.5667, "x": -4.21, "y": 25.11}, {"time": 1.8333, "x": -7.52, "y": -19.26}, {"time": 2.1667, "x": 2.33, "y": -0.16}, {"time": 2.3667, "x": -9.13, "y": 34.44}, {"time": 2.4667, "x": -14.63, "y": 39.1}, {"time": 2.5667, "x": -20.84, "y": 29.22}, {"time": 2.8333, "x": -35.7, "y": -16.56}], "scale": [{"time": 0.1667, "x": 1, "y": 1}, {"time": 0.8333, "x": 1.816, "y": 1.816}, {"time": 1.1667, "x": 1, "y": 1}, {"time": 1.8333, "x": 1.816, "y": 1.816}, {"time": 2.1667, "x": 1, "y": 1}, {"time": 2.8333, "x": 1.816, "y": 1.816}]}, "bone60": {"rotate": [{"time": 0, "angle": 31.56}, {"time": 0.6667, "angle": -5.45}, {"time": 0.8333, "angle": 31.56}, {"time": 1.5, "angle": 9.54}, {"time": 1.7333, "angle": 31.56}, {"time": 2.4, "angle": 9.54}, {"time": 2.6667, "angle": 31.56}, {"time": 3.3333, "angle": -62.87}], "translate": [{"time": 0, "x": 2.33, "y": -0.16}, {"time": 0.3, "x": -5.65, "y": 32.29}, {"time": 0.6667, "x": -13.32, "y": -17.8}, {"time": 0.8333, "x": 2.33, "y": -0.16}, {"time": 1.1333, "x": -2.29, "y": 33.13}, {"time": 1.5, "x": -7.52, "y": -19.26}, {"time": 1.7333, "x": 2.33, "y": -0.16}, {"time": 1.9667, "x": -1.52, "y": 29.97}, {"time": 2.0333, "x": -2.62, "y": 33.94}, {"time": 2.1333, "x": -4.21, "y": 25.11}, {"time": 2.4, "x": -7.52, "y": -19.26}, {"time": 2.6667, "x": 2.33, "y": -0.16}, {"time": 2.8667, "x": -9.13, "y": 34.44}, {"time": 2.9667, "x": -14.63, "y": 39.1}, {"time": 3.0667, "x": -20.84, "y": 29.22}, {"time": 3.3333, "x": -35.7, "y": -16.56}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1.816, "y": 1.816}, {"time": 0.8333, "x": 1, "y": 1}, {"time": 1.5, "x": 1.816, "y": 1.816}, {"time": 1.7333, "x": 1, "y": 1}, {"time": 2.4, "x": 1.816, "y": 1.816}, {"time": 2.6667, "x": 1, "y": 1}, {"time": 3.3333, "x": 1.816, "y": 1.816}]}}, "deform": {"default": {"backlight": {"backlight": [{"time": 0}, {"time": 0.3, "vertices": [20.88283, 15.05505, 18.94023, 1.45695, 39.82306, -19.42587, -9.7788, 5.92323, -19.91972, -5.87551, 18.92466, -2.30175]}, {"time": 0.7, "vertices": [-14.32194, -32.14153, -3.51952, 9.45057, -13.32829, 16.40406, -4.3201, -4.32475, -17.01181, -19.21142, -13.47219, -5.90404, -14.04189, 22.68305, -4.86065, -24.30324]}, {"time": 1.1, "offset": 2, "vertices": [8.15249, 1.6305, 0, 0, 0, 0, -6.05251, -16.52621, -15.79508, -12.72846]}, {"time": 1.4333, "vertices": [20.88283, 15.05505, 18.94023, 1.45695, 39.82306, -19.42587, -0.9713, 8.74164, -5.82774, -7.28473, 27.73215, -1.97862]}, {"time": 1.8333, "vertices": [-14.32194, -32.14153, -3.51952, 9.45057, -13.32829, 16.40406, -4.3201, -4.32475, -15.05853, -2.36696, -10.45653, -4.57128, -14.04189, 22.68305, -4.86065, -24.30324]}, {"time": 2.2333, "vertices": [2.32031, 1.67278, -8.42992, 9.71539, 4.9263, 5.86568, 3.22805, 16.69808, 0.98164, 6.69384, 1.91464, -0.49127, 0, 0, 0.95314, -3.81253]}, {"time": 2.5333, "vertices": [20.88283, 15.05505, 18.94023, 1.45695, 39.82306, -19.42587, -6.5469, 9.85677, -7.68626, -2.45256, 17.0075, 1.43838]}, {"time": 2.9333, "vertices": [-14.32194, -32.14153, -3.51952, 9.45057, -13.32829, 16.40406, -4.3201, -4.32475, -27.76245, -12.13448, -9.51494, -1.72744, -14.04189, 22.68305, -4.86065, -24.30324]}, {"time": 3.3333}]}}}, "drawOrder": [{"time": 0.3333, "offsets": [{"slot": "Nap tien34", "offset": 15}, {"slot": "Nap tien35", "offset": 15}, {"slot": "Nap tien36", "offset": 15}]}, {"time": 0.5, "offsets": [{"slot": "Nap tien34", "offset": -2}, {"slot": "Nap tien35", "offset": 15}, {"slot": "Nap tien36", "offset": 15}]}, {"time": 0.8667, "offsets": [{"slot": "Nap tien34", "offset": 17}]}, {"time": 1.3, "offsets": [{"slot": "Nap tien34", "offset": 15}, {"slot": "Nap tien35", "offset": 15}, {"slot": "Nap tien36", "offset": 15}]}, {"time": 1.5}, {"time": 1.6333, "offsets": [{"slot": "Nap tien34", "offset": 16}, {"slot": "Nap tien35", "offset": 16}]}, {"time": 2.0667}, {"time": 2.6333, "offsets": [{"slot": "Nap tien34", "offset": 15}, {"slot": "Nap tien35", "offset": 15}, {"slot": "Nap tien36", "offset": 15}]}, {"time": 2.7, "offsets": [{"slot": "Nap tien34", "offset": 16}, {"slot": "Nap tien35", "offset": 16}]}, {"time": 3.1, "offsets": [{"slot": "Nap tien34", "offset": 15}, {"slot": "Nap tien35", "offset": 15}, {"slot": "Nap tien36", "offset": 15}]}]}, "bottombar": {"slots": {"bongden0": {"attachment": [{"time": 0, "name": null}]}, "dassslight": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3333, "color": "ffffff00"}, {"time": 2.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.5, "color": "ffffffff"}, {"time": 3.8333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "dassslight"}]}}, "bones": {"bone63": {"translate": [{"time": 0, "x": -48.73, "y": -18.81}, {"time": 2, "x": 772, "y": -18.83}, {"time": 2.3333, "x": 1022.15, "y": -84.46}, {"time": 4, "x": 1456.07, "y": -85.66}], "scale": [{"time": 0, "x": 0.662, "y": 1}, {"time": 0.3333, "x": 2.072, "y": 1}, {"time": 0.6667, "x": 3.113, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 3.113, "y": 1}, {"time": 1.6667, "x": 2.072, "y": 1}, {"time": 2, "x": 0.662, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 0.662, "y": 1}, {"time": 2.6333, "x": 1.12, "y": 1}, {"time": 3.1667, "x": 1.81, "y": 1}, {"time": 3.7, "x": 1.12, "y": 1}, {"time": 4, "x": 0.662, "y": 1}]}}}, "button_naptien": {"slots": {"Nap tien2": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1, "color": "ffffffff"}, {"time": 0.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.4333, "color": "ffffffff"}, {"time": 1.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.9333, "color": "ffffff00"}, {"time": 2.0667, "color": "ffffffff"}, {"time": 2.1667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Nap tien2"}]}, "Nap tien3": {"attachment": [{"time": 0, "name": "Nap tien3"}]}, "Nap tien4": {"attachment": [{"time": 0, "name": "Nap tien4"}]}, "Nap tien5": {"attachment": [{"time": 0, "name": "Nap tien5"}]}, "Nap tien6": {"attachment": [{"time": 0, "name": "Nap tien6"}]}, "Nap tien7": {"attachment": [{"time": 0, "name": "Nap tien7"}]}, "Nap tien8": {"attachment": [{"time": 0, "name": "Nap tien8"}]}, "Nap tien9": {"attachment": [{"time": 0, "name": "Nap tien9"}]}, "Nap tien10": {"attachment": [{"time": 0, "name": "Nap tien10"}]}, "Nap tien11": {"attachment": [{"time": 0, "name": "Nap tien11"}]}, "Nap tien12": {"attachment": [{"time": 0, "name": "Nap tien12"}]}, "Nap tien13": {"attachment": [{"time": 0, "name": "Nap tien4"}]}, "Nap tien14": {"attachment": [{"time": 0, "name": "Nap tien7"}]}, "Nap tien15": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.0667, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.7667, "color": "ffffffff"}, {"time": 0.9, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4, "color": "ffffff00"}, {"time": 1.5, "color": "ffffffff"}, {"time": 1.6333, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 2.1, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Nap tien2"}]}, "Nap tien16": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1, "color": "ffffff00"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7333, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 0.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4333, "color": "ffffff00"}, {"time": 1.5667, "color": "ffffffff"}, {"time": 1.7, "color": "ffffff00", "curve": "stepped"}, {"time": 2.0667, "color": "ffffff00"}, {"time": 2.1667, "color": "ffffffff"}, {"time": 2.3, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Nap tien2"}]}, "Nap tien17": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 0.3, "color": "ffffffff"}, {"time": 0.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7667, "color": "ffffff00"}, {"time": 0.9, "color": "ffffffff"}, {"time": 1, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.6333, "color": "ffffffff"}, {"time": 1.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1, "color": "ffffff00"}, {"time": 2.2333, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Nap tien2"}]}, "Nap tien18": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}, {"time": 1.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5667, "color": "ffffff00"}, {"time": 1.7, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1667, "color": "ffffff00"}, {"time": 2.3, "color": "ffffffff"}, {"time": 2.4, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Nap tien2"}]}, "Nap tien19": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}, {"time": 0.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6333, "color": "ffffff00"}, {"time": 1.7667, "color": "ffffffff"}, {"time": 1.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2333, "color": "ffffff00"}, {"time": 2.3333, "color": "ffffffff"}, {"time": 2.4667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Nap tien2"}]}, "Nap tien20": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3667, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9667, "color": "ffffff00"}, {"time": 1.0667, "color": "ffffffff"}, {"time": 1.2, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7, "color": "ffffff00"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 1.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3, "color": "ffffff00"}, {"time": 2.4, "color": "ffffffff"}, {"time": 2.5333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Nap tien2"}]}, "Nap tien21": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4333, "color": "ffffff00"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffff00"}, {"time": 1.1333, "color": "ffffffff"}, {"time": 1.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7667, "color": "ffffff00"}, {"time": 1.8667, "color": "ffffffff"}, {"time": 2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3333, "color": "ffffff00"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Nap tien2"}]}, "Nap tien22": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0667, "color": "ffffff00"}, {"time": 1.2, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.8333, "color": "ffffff00"}, {"time": 1.9333, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.4, "color": "ffffff00"}, {"time": 2.5333, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Nap tien2"}]}, "Nap tien23": {"color": [{"time": 0, "color": "ffffff7d"}, {"time": 0.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1333, "color": "ffffff00"}, {"time": 1.2667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff7d"}, {"time": 1.4, "color": "ffffff00", "curve": "stepped"}, {"time": 1.8667, "color": "ffffff00"}, {"time": 2, "color": "ffffffff"}, {"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.4667, "color": "ffffff00"}, {"time": 2.6, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff7d"}], "attachment": [{"time": 0, "name": "Nap tien2"}]}, "Nap tien24": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2, "color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.9333, "color": "ffffff00"}, {"time": 2.0667, "color": "ffffffff"}, {"time": 2.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.5333, "color": "ffffff00"}, {"time": 2.6667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Nap tien2"}]}, "Nap tien25": {"color": [{"time": 0, "color": "ffffff83"}, {"time": 0.0667, "color": "ffffffff"}, {"time": 0.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.7667, "color": "ffffffff"}, {"time": 0.9, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2667, "color": "ffffff00"}, {"time": 1.3333, "color": "ffffff83"}, {"time": 1.4, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 2.1, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6, "color": "ffffff00"}, {"time": 2.6667, "color": "ffffff83"}], "attachment": [{"time": 0, "name": "Nap tien2"}]}, "Nap tien26": {"color": [{"time": 0, "color": "ffffff7b"}, {"time": 0.1, "color": "ffffffff"}, {"time": 0.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7333, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 0.9667, "color": "ffffff00"}, {"time": 1.3333, "color": "ffffff7b"}, {"time": 1.4333, "color": "ffffffff"}, {"time": 1.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.0667, "color": "ffffff00"}, {"time": 2.1667, "color": "ffffffff"}, {"time": 2.3, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Nap tien2"}]}, "Nap tien27": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.0667, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7667, "color": "ffffff00"}, {"time": 0.9, "color": "ffffffff"}, {"time": 1, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4, "color": "ffffff00"}, {"time": 1.5, "color": "ffffffff"}, {"time": 1.6333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1, "color": "ffffff00"}, {"time": 2.2333, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Nap tien2"}]}, "Nap tien28": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1, "color": "ffffff00"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}, {"time": 1.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4333, "color": "ffffff00"}, {"time": 1.5667, "color": "ffffffff"}, {"time": 1.7, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1667, "color": "ffffff00"}, {"time": 2.3, "color": "ffffffff"}, {"time": 2.4, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Nap tien2"}]}, "Nap tien29": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 0.3, "color": "ffffffff"}, {"time": 0.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.6333, "color": "ffffffff"}, {"time": 1.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2333, "color": "ffffff00"}, {"time": 2.3333, "color": "ffffffff"}, {"time": 2.4667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Nap tien2"}]}, "Nap tien30": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9667, "color": "ffffff00"}, {"time": 1.0667, "color": "ffffffff"}, {"time": 1.2, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5667, "color": "ffffff00"}, {"time": 1.7, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3, "color": "ffffff00"}, {"time": 2.4, "color": "ffffffff"}, {"time": 2.5333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Nap tien2"}]}, "Nap tien31": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}, {"time": 0.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffff00"}, {"time": 1.1333, "color": "ffffffff"}, {"time": 1.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6333, "color": "ffffff00"}, {"time": 1.7667, "color": "ffffffff"}, {"time": 1.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3333, "color": "ffffff00"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Nap tien2"}]}, "Nap tien32": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3667, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0667, "color": "ffffff00"}, {"time": 1.2, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7, "color": "ffffff00"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 1.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.4, "color": "ffffff00"}, {"time": 2.5333, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Nap tien2"}]}, "Nap tien33": {"attachment": [{"time": 0, "name": "Nap tien5"}]}, "arrow1": {"attachment": [{"time": 0, "name": "arrow1"}]}, "arrow2": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}, {"time": 2.3333, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "arrow2"}]}, "arrow3": {"attachment": [{"time": 0, "name": "arrow1"}]}, "arrow4": {"color": [{"time": 0, "color": "ffffff81"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 2.1667, "color": "ffffff00"}, {"time": 2.5, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff81"}], "attachment": [{"time": 0, "name": "arrow2"}]}, "arrow5": {"attachment": [{"time": 0, "name": "arrow1"}]}, "arrow6": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00"}, {"time": 2, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00"}, {"time": 2.6667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "arrow2"}]}, "bongden0": {"attachment": [{"time": 0, "name": null}]}, "dennaptien": {"attachment": [{"time": 0, "name": "<PERSON><PERSON><PERSON>"}]}, "star": {"attachment": [{"time": 0, "name": "star"}]}, "star2": {"attachment": [{"time": 0, "name": "star"}]}}, "bones": {"bone31": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": -0.86, "y": 6.06}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 1, "x": -0.32, "y": 2.85}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 1.6667, "x": -0.86, "y": 6.06}, {"time": 2, "x": 0, "y": 0}, {"time": 2.3333, "x": -0.32, "y": 2.85}, {"time": 2.6667, "x": 0, "y": 0}]}, "bone32": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2, "angle": -5.43}, {"time": 0.4667, "angle": 1.39}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}, {"time": 1.5333, "angle": -5.43}, {"time": 1.8, "angle": 1.39}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": -0.2, "y": 1.57}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 0.9, "x": -2.57, "y": -0.43}, {"time": 1.1333, "x": 1.84, "y": 0.87}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 1.6667, "x": -0.2, "y": 1.57}, {"time": 2, "x": 0, "y": 0}]}, "bone33": {"translate": [{"time": 0, "x": -0.31, "y": 0.04, "curve": "stepped"}, {"time": 0.3333, "x": -0.31, "y": 0.04}, {"time": 0.4333, "x": 0.17, "y": 5.18}, {"time": 0.5, "x": 0.31, "y": 6.77}, {"time": 0.5667, "x": 0.42, "y": 7.93}, {"time": 0.8333, "x": -0.31, "y": 0.04, "curve": "stepped"}, {"time": 1.6667, "x": -0.31, "y": 0.04}, {"time": 1.7667, "x": 0.17, "y": 5.18}, {"time": 1.8333, "x": 0.31, "y": 6.77}, {"time": 1.9, "x": 0.42, "y": 7.93}, {"time": 2.1667, "x": -0.31, "y": 0.04}]}, "bone34": {"rotate": [{"time": 0, "angle": 31.55}], "translate": [{"time": 0, "x": -0.31, "y": 0.04, "curve": "stepped"}, {"time": 0.3333, "x": -0.31, "y": 0.04}, {"time": 0.4333, "x": 0.17, "y": 5.18}, {"time": 0.5, "x": 0.31, "y": 6.77}, {"time": 0.5667, "x": 0.42, "y": 7.93}, {"time": 0.8333, "x": -0.31, "y": 0.04, "curve": "stepped"}, {"time": 1.6667, "x": -0.31, "y": 0.04}, {"time": 1.7667, "x": 0.17, "y": 5.18}, {"time": 1.8333, "x": 0.31, "y": 6.77}, {"time": 1.9, "x": 0.42, "y": 7.93}, {"time": 2.1667, "x": -0.31, "y": 0.04}]}, "bone35": {"rotate": [{"time": 0.3333, "angle": 0}, {"time": 0.6667, "angle": -178.36}, {"time": 0.8333, "angle": 92.48}, {"time": 1, "angle": 0}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5, "x": 1.135, "y": 1.135}, {"time": 0.6667, "x": 1.808, "y": 1.808}, {"time": 0.8333, "x": 1.278, "y": 1.278}, {"time": 1, "x": 0, "y": 0}]}, "bone36": {"rotate": [{"time": 1.5, "angle": 0}, {"time": 1.8333, "angle": -178.36}, {"time": 2, "angle": 92.48}, {"time": 2.1667, "angle": 0}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0}, {"time": 1.6667, "x": 1.135, "y": 1.135}, {"time": 1.8333, "x": 1.808, "y": 1.808}, {"time": 2, "x": 1.278, "y": 1.278}, {"time": 2.1667, "x": 0, "y": 0}]}, "bone4": {"scale": [{"time": 0.5, "x": 1, "y": 1}, {"time": 0.8333, "x": 1.161, "y": 1.161}, {"time": 1.1667, "x": 1, "y": 1}]}, "bone5": {"scale": [{"time": 0.6667, "x": 1, "y": 1}, {"time": 1, "x": 1.161, "y": 1.161}, {"time": 1.3333, "x": 1, "y": 1}]}, "bone6": {"scale": [{"time": 0.8333, "x": 1, "y": 1}, {"time": 1.1667, "x": 1.161, "y": 1.161}, {"time": 1.5, "x": 1, "y": 1}]}, "bone7": {"scale": [{"time": 1, "x": 1, "y": 1}, {"time": 1.3333, "x": 1.161, "y": 1.161}, {"time": 1.6667, "x": 1, "y": 1}]}, "bone8": {"scale": [{"time": 1.1667, "x": 1, "y": 1}, {"time": 1.5, "x": 1.161, "y": 1.161}, {"time": 1.8333, "x": 1, "y": 1}]}, "bone9": {"scale": [{"time": 1.3333, "x": 1, "y": 1}, {"time": 1.6667, "x": 1.161, "y": 1.161}, {"time": 2, "x": 1, "y": 1}]}, "bone10": {"scale": [{"time": 1.5, "x": 1, "y": 1}, {"time": 1.8333, "x": 1.161, "y": 1.161}, {"time": 2.1667, "x": 1, "y": 1}]}}}, "headerbar": {"slots": {"bongden0": {"attachment": [{"time": 0, "name": null}]}, "dassslight": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffff00"}, {"time": 0.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1333, "color": "ffffffff"}, {"time": 1.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 2.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.5667, "color": "ffffffff"}, {"time": 4, "color": "ffffff00", "curve": "stepped"}, {"time": 4.3667, "color": "ffffff00"}, {"time": 4.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 5.3, "color": "ffffffff"}, {"time": 5.6, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "dassslight"}]}}, "bones": {"bone63": {"translate": [{"time": 0, "x": -150.6, "y": -86.91}, {"time": 0.3, "x": -64.66, "y": -86.91}, {"time": 1.6, "x": 183.96, "y": -86.91}, {"time": 1.7333, "x": 244.39, "y": -67.51}, {"time": 4, "x": 983.83, "y": -67.51}, {"time": 4.1667, "x": 1061.52, "y": -86.91}, {"time": 5.7667, "x": 1419.01, "y": -86.91}], "scale": [{"time": 0, "x": 0.662, "y": 1}, {"time": 0.3, "x": 0.764, "y": 1}, {"time": 0.8, "x": 0.977, "y": 1}, {"time": 1.3, "x": 0.87, "y": 1}, {"time": 1.6, "x": 0.662, "y": 1}, {"time": 1.7333, "x": 0.662, "y": 0.466}, {"time": 2.1667, "x": 0.66, "y": 0.513}, {"time": 2.5333, "x": 1.281, "y": 0.513, "curve": "stepped"}, {"time": 3.2, "x": 1.281, "y": 0.513}, {"time": 3.5667, "x": 0.66, "y": 0.513}, {"time": 4, "x": 0.66, "y": 1}, {"time": 4.1667, "x": 0.662, "y": 1}, {"time": 4.5, "x": 0.764, "y": 1}, {"time": 4.9667, "x": 0.977, "y": 1}, {"time": 5.4667, "x": 0.87, "y": 1}, {"time": 5.7667, "x": 0.662, "y": 1}]}}}, "loading_smallchip": {"slots": {"bongden0": {"attachment": [{"time": 0, "name": null}]}, "chiploading4": {"attachment": [{"time": 0, "name": "chiploading4"}, {"time": 0.0667, "name": "chiploading3"}, {"time": 0.1333, "name": "chiploading2"}, {"time": 0.2, "name": "chiploading1"}, {"time": 0.2667, "name": "chiploading0"}, {"time": 0.3333, "name": "chiploading5"}, {"time": 0.4, "name": "chiploading6"}, {"time": 0.4667, "name": "chiploading7"}, {"time": 0.5333, "name": "chiploading4"}, {"time": 0.6, "name": "chiploading3"}, {"time": 0.6667, "name": "chiploading2"}, {"time": 0.7333, "name": "chiploading1"}, {"time": 0.8, "name": "chiploading0"}, {"time": 0.8667, "name": "chiploading5"}, {"time": 0.9333, "name": "chiploading6"}, {"time": 1, "name": "chiploading7"}, {"time": 1.0667, "name": "chiploading4"}, {"time": 1.1333, "name": "chiploading3"}, {"time": 1.2, "name": "chiploading2"}, {"time": 1.2667, "name": "chiploading1"}, {"time": 1.3333, "name": "chiploading0"}, {"time": 1.4, "name": "chiploading5"}, {"time": 1.4667, "name": "chiploading6"}, {"time": 1.5333, "name": "chiploading7"}, {"time": 1.6, "name": "chiploading4"}, {"time": 1.6667, "name": "chiploading3"}, {"time": 1.7333, "name": "chiploading2"}, {"time": 1.8, "name": "chiploading1"}, {"time": 1.8667, "name": "chiploading0"}, {"time": 1.9333, "name": "chiploading5"}, {"time": 2, "name": "chiploading6"}, {"time": 2.0667, "name": "chiploading7"}]}, "chiploading5": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.2667, "color": "ffffff00"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00"}, {"time": 1.0667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.6, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00"}, {"time": 2.1, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "chiploading4"}, {"time": 0.0667, "name": "chiploading3"}, {"time": 0.1333, "name": "chiploading2"}, {"time": 0.2, "name": "chiploading1"}, {"time": 0.2667, "name": "chiploading0"}, {"time": 0.3333, "name": "chiploading5"}, {"time": 0.4, "name": "chiploading6"}, {"time": 0.4667, "name": "chiploading7"}, {"time": 0.5333, "name": "chiploading4"}, {"time": 0.6, "name": "chiploading3"}, {"time": 0.6667, "name": "chiploading2"}, {"time": 0.7333, "name": "chiploading1"}, {"time": 0.8, "name": "chiploading0"}, {"time": 0.8667, "name": "chiploading5"}, {"time": 0.9333, "name": "chiploading6"}, {"time": 1, "name": "chiploading7"}, {"time": 1.0667, "name": "chiploading4"}, {"time": 1.1333, "name": "chiploading3"}, {"time": 1.2, "name": "chiploading2"}, {"time": 1.2667, "name": "chiploading1"}, {"time": 1.3333, "name": "chiploading0"}, {"time": 1.4, "name": "chiploading5"}, {"time": 1.4667, "name": "chiploading6"}, {"time": 1.5333, "name": "chiploading7"}, {"time": 1.6, "name": "chiploading4"}, {"time": 1.6667, "name": "chiploading3"}, {"time": 1.7333, "name": "chiploading2"}, {"time": 1.8, "name": "chiploading1"}, {"time": 1.8667, "name": "chiploading0"}, {"time": 1.9333, "name": "chiploading5"}, {"time": 2, "name": "chiploading6"}, {"time": 2.0667, "name": "chiploading7"}]}, "chiploading8": {"attachment": [{"time": 0, "name": "chiploading5"}, {"time": 0.2667, "name": null}, {"time": 0.3333, "name": "chiploading6"}, {"time": 0.8, "name": null}, {"time": 0.8667, "name": "chiploading8"}, {"time": 1.3333, "name": null}, {"time": 1.4, "name": "chiploading7"}, {"time": 1.8667, "name": null}, {"time": 1.9333, "name": "chiploading5"}]}, "chiploading9": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.2667, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1.1, "color": "ffffff00"}, {"time": 1.3667, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00"}, {"time": 1.9, "color": "ffffffff"}, {"time": 2.1, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "chiploading5"}, {"time": 0.2667, "name": null}, {"time": 0.3333, "name": "chiploading6"}, {"time": 0.8, "name": null}, {"time": 0.8667, "name": "chiploading8"}, {"time": 1.3333, "name": null}, {"time": 1.4, "name": "chiploading7"}, {"time": 1.8667, "name": null}, {"time": 1.9333, "name": "chiploading5"}]}, "chiploading10": {"color": [{"time": 0, "color": "ffffff2e"}, {"time": 0.2667, "color": "ffffff00"}, {"time": 0.5667, "color": "ffffff38"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1.1, "color": "ffffff40"}, {"time": 1.3667, "color": "ffffff00"}, {"time": 1.6333, "color": "ffffff37"}, {"time": 1.9, "color": "ffffff00"}, {"time": 2.1, "color": "ffffff1c"}], "attachment": [{"time": 0, "name": "chiploading5"}, {"time": 0.2667, "name": null}, {"time": 0.3333, "name": "chiploading6"}, {"time": 0.8, "name": null}, {"time": 0.8667, "name": "chiploading8"}, {"time": 1.3333, "name": null}, {"time": 1.4, "name": "chiploading7"}, {"time": 1.8667, "name": null}, {"time": 1.9333, "name": "chiploading5"}]}}, "bones": {"bone89": {"translate": [{"time": 0.0333, "x": 0, "y": 0}, {"time": 0.0667, "x": 2.53, "y": 0, "curve": "stepped"}, {"time": 0.1, "x": 2.53, "y": 0}, {"time": 0.1333, "x": 4.08, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 4.08, "y": 0}, {"time": 0.2, "x": 4.81, "y": 0, "curve": "stepped"}, {"time": 0.2333, "x": 4.81, "y": 0}, {"time": 0.3333, "x": -4.81, "y": 0, "curve": "stepped"}, {"time": 0.3667, "x": -4.81, "y": 0}, {"time": 0.4, "x": -5.31, "y": 0, "curve": "stepped"}, {"time": 0.4333, "x": -5.31, "y": 0}, {"time": 0.4667, "x": -4.15, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": -4.15, "y": 0}, {"time": 0.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5667, "x": 0, "y": 0}, {"time": 0.6, "x": 2.53, "y": 0, "curve": "stepped"}, {"time": 0.6333, "x": 2.53, "y": 0}, {"time": 0.6667, "x": 4.08, "y": 0, "curve": "stepped"}, {"time": 0.7, "x": 4.08, "y": 0}, {"time": 0.7333, "x": 4.81, "y": 0, "curve": "stepped"}, {"time": 0.7667, "x": 4.81, "y": 0}, {"time": 0.8667, "x": -4.81, "y": 0, "curve": "stepped"}, {"time": 0.9, "x": -4.81, "y": 0}, {"time": 0.9333, "x": -5.15, "y": 0, "curve": "stepped"}, {"time": 0.9667, "x": -5.15, "y": 0}, {"time": 1, "x": -4.81, "y": 0, "curve": "stepped"}, {"time": 1.0333, "x": -4.81, "y": 0}, {"time": 1.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1, "x": 0, "y": 0}, {"time": 1.1333, "x": 2.53, "y": 0, "curve": "stepped"}, {"time": 1.1667, "x": 2.53, "y": 0}, {"time": 1.2, "x": 4.08, "y": 0, "curve": "stepped"}, {"time": 1.2333, "x": 4.08, "y": 0}, {"time": 1.2667, "x": 4.81, "y": 0, "curve": "stepped"}, {"time": 1.3, "x": 4.81, "y": 0}, {"time": 1.4, "x": -4.81, "y": 0, "curve": "stepped"}, {"time": 1.4333, "x": -4.81, "y": 0}, {"time": 1.4667, "x": -5.31, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": -5.31, "y": 0}, {"time": 1.5333, "x": -4.48, "y": 0, "curve": "stepped"}, {"time": 1.5667, "x": -4.48, "y": 0}, {"time": 1.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 1.6667, "x": 2.53, "y": 0, "curve": "stepped"}, {"time": 1.7, "x": 2.53, "y": 0}, {"time": 1.7333, "x": 4.08, "y": 0, "curve": "stepped"}, {"time": 1.7667, "x": 4.08, "y": 0}, {"time": 1.8, "x": 4.81, "y": 0, "curve": "stepped"}, {"time": 1.8333, "x": 4.81, "y": 0}, {"time": 1.9333, "x": -4.81, "y": 0, "curve": "stepped"}, {"time": 1.9667, "x": -4.81, "y": 0}, {"time": 2, "x": -5.31, "y": 0, "curve": "stepped"}, {"time": 2.0333, "x": -5.31, "y": 0}, {"time": 2.0667, "x": -4.81, "y": 0}], "scale": [{"time": 0.0333, "x": 1, "y": 1}, {"time": 0.0667, "x": 1, "y": 0.857, "curve": "stepped"}, {"time": 0.1, "x": 1, "y": 0.857}, {"time": 0.1333, "x": 1, "y": 0.598, "curve": "stepped"}, {"time": 0.1667, "x": 1, "y": 0.598}, {"time": 0.2, "x": 1, "y": 0.254, "curve": "stepped"}, {"time": 0.2333, "x": 1, "y": 0.254}, {"time": 0.2667, "x": 1, "y": 0.002, "curve": "stepped"}, {"time": 0.3, "x": 1, "y": 0.002}, {"time": 0.3333, "x": 1, "y": 0.254, "curve": "stepped"}, {"time": 0.3667, "x": 1, "y": 0.254}, {"time": 0.4, "x": 1, "y": 0.516, "curve": "stepped"}, {"time": 0.4333, "x": 1, "y": 0.516}, {"time": 0.4667, "x": 1, "y": 0.79, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 0.79}, {"time": 0.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5667, "x": 1, "y": 1}, {"time": 0.6, "x": 1, "y": 0.857, "curve": "stepped"}, {"time": 0.6333, "x": 1, "y": 0.857}, {"time": 0.6667, "x": 1, "y": 0.598, "curve": "stepped"}, {"time": 0.7, "x": 1, "y": 0.598}, {"time": 0.7333, "x": 1, "y": 0.254, "curve": "stepped"}, {"time": 0.7667, "x": 1, "y": 0.254}, {"time": 0.8, "x": 1, "y": 0.002, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 0.002}, {"time": 0.8667, "x": 1, "y": 0.254, "curve": "stepped"}, {"time": 0.9, "x": 1, "y": 0.254}, {"time": 0.9333, "x": 1, "y": 0.572}, {"time": 0.9667, "x": 1, "y": 0.531}, {"time": 1, "x": 1, "y": 0.79, "curve": "stepped"}, {"time": 1.0333, "x": 1, "y": 0.79}, {"time": 1.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.1, "x": 1, "y": 1}, {"time": 1.1333, "x": 1, "y": 0.857, "curve": "stepped"}, {"time": 1.1667, "x": 1, "y": 0.857}, {"time": 1.2, "x": 1, "y": 0.598, "curve": "stepped"}, {"time": 1.2333, "x": 1, "y": 0.598}, {"time": 1.2667, "x": 1, "y": 0.254, "curve": "stepped"}, {"time": 1.3, "x": 1, "y": 0.254}, {"time": 1.3333, "x": 1, "y": 0.002, "curve": "stepped"}, {"time": 1.3667, "x": 1, "y": 0.002}, {"time": 1.4, "x": 1, "y": 0.254, "curve": "stepped"}, {"time": 1.4333, "x": 1, "y": 0.254}, {"time": 1.4667, "x": 1, "y": 0.572, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 0.572}, {"time": 1.5333, "x": 1, "y": 0.79, "curve": "stepped"}, {"time": 1.5667, "x": 1, "y": 0.79}, {"time": 1.6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6333, "x": 1, "y": 1}, {"time": 1.6667, "x": 1, "y": 0.857, "curve": "stepped"}, {"time": 1.7, "x": 1, "y": 0.857}, {"time": 1.7333, "x": 1, "y": 0.598, "curve": "stepped"}, {"time": 1.7667, "x": 1, "y": 0.598}, {"time": 1.8, "x": 1, "y": 0.254, "curve": "stepped"}, {"time": 1.8333, "x": 1, "y": 0.254}, {"time": 1.8667, "x": 1, "y": 0.002, "curve": "stepped"}, {"time": 1.9, "x": 1, "y": 0.002}, {"time": 1.9333, "x": 1, "y": 0.254, "curve": "stepped"}, {"time": 1.9667, "x": 1, "y": 0.254}, {"time": 2, "x": 1, "y": 0.572, "curve": "stepped"}, {"time": 2.0333, "x": 1, "y": 0.572}, {"time": 2.0667, "x": 1, "y": 0.79}]}, "bone90": {"translate": [{"time": 0.0333, "x": 0, "y": 0}, {"time": 0.0667, "x": 2.53, "y": 0, "curve": "stepped"}, {"time": 0.1, "x": 2.53, "y": 0}, {"time": 0.1333, "x": 4.08, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 4.08, "y": 0}, {"time": 0.2, "x": 4.81, "y": 0, "curve": "stepped"}, {"time": 0.2333, "x": 4.81, "y": 0}, {"time": 0.3333, "x": -4.81, "y": 0, "curve": "stepped"}, {"time": 0.3667, "x": -4.81, "y": 0}, {"time": 0.4, "x": -5.31, "y": 0, "curve": "stepped"}, {"time": 0.4333, "x": -5.31, "y": 0}, {"time": 0.4667, "x": -4.15, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": -4.15, "y": 0}, {"time": 0.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5667, "x": 0, "y": 0}, {"time": 0.6, "x": 2.53, "y": 0, "curve": "stepped"}, {"time": 0.6333, "x": 2.53, "y": 0}, {"time": 0.6667, "x": 4.08, "y": 0, "curve": "stepped"}, {"time": 0.7, "x": 4.08, "y": 0}, {"time": 0.7333, "x": 4.81, "y": 0, "curve": "stepped"}, {"time": 0.7667, "x": 4.81, "y": 0}, {"time": 0.8667, "x": -4.81, "y": 0, "curve": "stepped"}, {"time": 0.9, "x": -4.81, "y": 0}, {"time": 0.9333, "x": -5.15, "y": 0, "curve": "stepped"}, {"time": 0.9667, "x": -5.15, "y": 0}, {"time": 1, "x": -4.81, "y": 0, "curve": "stepped"}, {"time": 1.0333, "x": -4.81, "y": 0}, {"time": 1.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1, "x": 0, "y": 0}, {"time": 1.1333, "x": 2.53, "y": 0, "curve": "stepped"}, {"time": 1.1667, "x": 2.53, "y": 0}, {"time": 1.2, "x": 4.08, "y": 0, "curve": "stepped"}, {"time": 1.2333, "x": 4.08, "y": 0}, {"time": 1.2667, "x": 4.81, "y": 0, "curve": "stepped"}, {"time": 1.3, "x": 4.81, "y": 0}, {"time": 1.4, "x": -4.81, "y": 0, "curve": "stepped"}, {"time": 1.4333, "x": -4.81, "y": 0}, {"time": 1.4667, "x": -5.31, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": -5.31, "y": 0}, {"time": 1.5333, "x": -4.48, "y": 0, "curve": "stepped"}, {"time": 1.5667, "x": -4.48, "y": 0}, {"time": 1.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 1.6667, "x": 2.53, "y": 0, "curve": "stepped"}, {"time": 1.7, "x": 2.53, "y": 0}, {"time": 1.7333, "x": 4.08, "y": 0, "curve": "stepped"}, {"time": 1.7667, "x": 4.08, "y": 0}, {"time": 1.8, "x": 4.81, "y": 0, "curve": "stepped"}, {"time": 1.8333, "x": 4.81, "y": 0}, {"time": 1.9333, "x": -4.81, "y": 0, "curve": "stepped"}, {"time": 1.9667, "x": -4.81, "y": 0}, {"time": 2, "x": -5.31, "y": 0, "curve": "stepped"}, {"time": 2.0333, "x": -5.31, "y": 0}, {"time": 2.0667, "x": -4.81, "y": 0}], "scale": [{"time": 0.0333, "x": 1, "y": 1}, {"time": 0.0667, "x": 1, "y": 0.857, "curve": "stepped"}, {"time": 0.1, "x": 1, "y": 0.857}, {"time": 0.1333, "x": 1, "y": 0.598, "curve": "stepped"}, {"time": 0.1667, "x": 1, "y": 0.598}, {"time": 0.2, "x": 1, "y": 0.254, "curve": "stepped"}, {"time": 0.2333, "x": 1, "y": 0.254}, {"time": 0.2667, "x": 1, "y": 0.002, "curve": "stepped"}, {"time": 0.3, "x": 1, "y": 0.002}, {"time": 0.3333, "x": 1, "y": 0.254, "curve": "stepped"}, {"time": 0.3667, "x": 1, "y": 0.254}, {"time": 0.4, "x": 1, "y": 0.516, "curve": "stepped"}, {"time": 0.4333, "x": 1, "y": 0.516}, {"time": 0.4667, "x": 1, "y": 0.79, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 0.79}, {"time": 0.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5667, "x": 1, "y": 1}, {"time": 0.6, "x": 1, "y": 0.857, "curve": "stepped"}, {"time": 0.6333, "x": 1, "y": 0.857}, {"time": 0.6667, "x": 1, "y": 0.598, "curve": "stepped"}, {"time": 0.7, "x": 1, "y": 0.598}, {"time": 0.7333, "x": 1, "y": 0.254, "curve": "stepped"}, {"time": 0.7667, "x": 1, "y": 0.254}, {"time": 0.8, "x": 1, "y": 0.002, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 0.002}, {"time": 0.8667, "x": 1, "y": 0.254, "curve": "stepped"}, {"time": 0.9, "x": 1, "y": 0.254}, {"time": 0.9333, "x": 1, "y": 0.572, "curve": "stepped"}, {"time": 0.9667, "x": 1, "y": 0.572}, {"time": 1, "x": 1, "y": 0.79, "curve": "stepped"}, {"time": 1.0333, "x": 1, "y": 0.79}, {"time": 1.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.1, "x": 1, "y": 1}, {"time": 1.1333, "x": 1, "y": 0.857, "curve": "stepped"}, {"time": 1.1667, "x": 1, "y": 0.857}, {"time": 1.2, "x": 1, "y": 0.598, "curve": "stepped"}, {"time": 1.2333, "x": 1, "y": 0.598}, {"time": 1.2667, "x": 1, "y": 0.254, "curve": "stepped"}, {"time": 1.3, "x": 1, "y": 0.254}, {"time": 1.3333, "x": 1, "y": 0.002, "curve": "stepped"}, {"time": 1.3667, "x": 1, "y": 0.002}, {"time": 1.4, "x": 1, "y": 0.254, "curve": "stepped"}, {"time": 1.4333, "x": 1, "y": 0.254}, {"time": 1.4667, "x": 1, "y": 0.572, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 0.572}, {"time": 1.5333, "x": 1, "y": 0.79, "curve": "stepped"}, {"time": 1.5667, "x": 1, "y": 0.79}, {"time": 1.6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6333, "x": 1, "y": 1}, {"time": 1.6667, "x": 1, "y": 0.857, "curve": "stepped"}, {"time": 1.7, "x": 1, "y": 0.857}, {"time": 1.7333, "x": 1, "y": 0.598, "curve": "stepped"}, {"time": 1.7667, "x": 1, "y": 0.598}, {"time": 1.8, "x": 1, "y": 0.254, "curve": "stepped"}, {"time": 1.8333, "x": 1, "y": 0.254}, {"time": 1.8667, "x": 1, "y": 0.002, "curve": "stepped"}, {"time": 1.9, "x": 1, "y": 0.002}, {"time": 1.9333, "x": 1, "y": 0.254, "curve": "stepped"}, {"time": 1.9667, "x": 1, "y": 0.254}, {"time": 2, "x": 1, "y": 0.572, "curve": "stepped"}, {"time": 2.0333, "x": 1, "y": 0.572}, {"time": 2.0667, "x": 1, "y": 0.79}]}, "bone91": {"translate": [{"time": 0.0333, "x": 0, "y": 0}, {"time": 0.0667, "x": 2.53, "y": 0, "curve": "stepped"}, {"time": 0.1, "x": 2.53, "y": 0}, {"time": 0.1333, "x": 4.08, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 4.08, "y": 0}, {"time": 0.2, "x": 4.81, "y": 0, "curve": "stepped"}, {"time": 0.2333, "x": 4.81, "y": 0}, {"time": 0.3333, "x": -4.81, "y": 0, "curve": "stepped"}, {"time": 0.3667, "x": -4.81, "y": 0}, {"time": 0.4, "x": -5.31, "y": 0, "curve": "stepped"}, {"time": 0.4333, "x": -5.31, "y": 0}, {"time": 0.4667, "x": -4.15, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": -4.15, "y": 0}, {"time": 0.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5667, "x": 0, "y": 0}, {"time": 0.6, "x": 2.53, "y": 0, "curve": "stepped"}, {"time": 0.6333, "x": 2.53, "y": 0}, {"time": 0.6667, "x": 4.08, "y": 0, "curve": "stepped"}, {"time": 0.7, "x": 4.08, "y": 0}, {"time": 0.7333, "x": 4.81, "y": 0, "curve": "stepped"}, {"time": 0.7667, "x": 4.81, "y": 0}, {"time": 0.8667, "x": -4.81, "y": 0, "curve": "stepped"}, {"time": 0.9, "x": -4.81, "y": 0}, {"time": 0.9333, "x": -5.15, "y": 0, "curve": "stepped"}, {"time": 0.9667, "x": -5.15, "y": 0}, {"time": 1, "x": -4.81, "y": 0, "curve": "stepped"}, {"time": 1.0333, "x": -4.81, "y": 0}, {"time": 1.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1, "x": 0, "y": 0}, {"time": 1.1333, "x": 2.53, "y": 0, "curve": "stepped"}, {"time": 1.1667, "x": 2.53, "y": 0}, {"time": 1.2, "x": 4.08, "y": 0, "curve": "stepped"}, {"time": 1.2333, "x": 4.08, "y": 0}, {"time": 1.2667, "x": 4.81, "y": 0, "curve": "stepped"}, {"time": 1.3, "x": 4.81, "y": 0}, {"time": 1.4, "x": -4.81, "y": 0, "curve": "stepped"}, {"time": 1.4333, "x": -4.81, "y": 0}, {"time": 1.4667, "x": -5.31, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": -5.31, "y": 0}, {"time": 1.5333, "x": -4.48, "y": 0, "curve": "stepped"}, {"time": 1.5667, "x": -4.48, "y": 0}, {"time": 1.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 1.6667, "x": 2.53, "y": 0, "curve": "stepped"}, {"time": 1.7, "x": 2.53, "y": 0}, {"time": 1.7333, "x": 4.08, "y": 0, "curve": "stepped"}, {"time": 1.7667, "x": 4.08, "y": 0}, {"time": 1.8, "x": 4.81, "y": 0, "curve": "stepped"}, {"time": 1.8333, "x": 4.81, "y": 0}, {"time": 1.9333, "x": -4.81, "y": 0, "curve": "stepped"}, {"time": 1.9667, "x": -4.81, "y": 0}, {"time": 2, "x": -5.31, "y": 0, "curve": "stepped"}, {"time": 2.0333, "x": -5.31, "y": 0}, {"time": 2.0667, "x": -4.81, "y": 0}], "scale": [{"time": 0.0333, "x": 1, "y": 1}, {"time": 0.0667, "x": 1, "y": 0.857, "curve": "stepped"}, {"time": 0.1, "x": 1, "y": 0.857}, {"time": 0.1333, "x": 1, "y": 0.598, "curve": "stepped"}, {"time": 0.1667, "x": 1, "y": 0.598}, {"time": 0.2, "x": 1, "y": 0.254, "curve": "stepped"}, {"time": 0.2333, "x": 1, "y": 0.254}, {"time": 0.2667, "x": 1, "y": 0.002, "curve": "stepped"}, {"time": 0.3, "x": 1, "y": 0.002}, {"time": 0.3333, "x": 1, "y": 0.254, "curve": "stepped"}, {"time": 0.3667, "x": 1, "y": 0.254}, {"time": 0.4, "x": 1, "y": 0.516, "curve": "stepped"}, {"time": 0.4333, "x": 1, "y": 0.516}, {"time": 0.4667, "x": 1, "y": 0.79, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 0.79}, {"time": 0.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5667, "x": 1, "y": 1}, {"time": 0.6, "x": 1, "y": 0.857, "curve": "stepped"}, {"time": 0.6333, "x": 1, "y": 0.857}, {"time": 0.6667, "x": 1, "y": 0.598, "curve": "stepped"}, {"time": 0.7, "x": 1, "y": 0.598}, {"time": 0.7333, "x": 1, "y": 0.254, "curve": "stepped"}, {"time": 0.7667, "x": 1, "y": 0.254}, {"time": 0.8, "x": 1, "y": 0.002, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 0.002}, {"time": 0.8667, "x": 1, "y": 0.254, "curve": "stepped"}, {"time": 0.9, "x": 1, "y": 0.254}, {"time": 0.9333, "x": 1, "y": 0.572, "curve": "stepped"}, {"time": 0.9667, "x": 1, "y": 0.572}, {"time": 1, "x": 1, "y": 0.79, "curve": "stepped"}, {"time": 1.0333, "x": 1, "y": 0.79}, {"time": 1.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.1, "x": 1, "y": 1}, {"time": 1.1333, "x": 1, "y": 0.857, "curve": "stepped"}, {"time": 1.1667, "x": 1, "y": 0.857}, {"time": 1.2, "x": 1, "y": 0.598, "curve": "stepped"}, {"time": 1.2333, "x": 1, "y": 0.598}, {"time": 1.2667, "x": 1, "y": 0.254, "curve": "stepped"}, {"time": 1.3, "x": 1, "y": 0.254}, {"time": 1.3333, "x": 1, "y": 0.002, "curve": "stepped"}, {"time": 1.3667, "x": 1, "y": 0.002}, {"time": 1.4, "x": 1, "y": 0.254, "curve": "stepped"}, {"time": 1.4333, "x": 1, "y": 0.254}, {"time": 1.4667, "x": 1, "y": 0.572, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 0.572}, {"time": 1.5333, "x": 1, "y": 0.79, "curve": "stepped"}, {"time": 1.5667, "x": 1, "y": 0.79}, {"time": 1.6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6333, "x": 1, "y": 1}, {"time": 1.6667, "x": 1, "y": 0.857, "curve": "stepped"}, {"time": 1.7, "x": 1, "y": 0.857}, {"time": 1.7333, "x": 1, "y": 0.598, "curve": "stepped"}, {"time": 1.7667, "x": 1, "y": 0.598}, {"time": 1.8, "x": 1, "y": 0.254, "curve": "stepped"}, {"time": 1.8333, "x": 1, "y": 0.254}, {"time": 1.8667, "x": 1, "y": 0.002, "curve": "stepped"}, {"time": 1.9, "x": 1, "y": 0.002}, {"time": 1.9333, "x": 1, "y": 0.254, "curve": "stepped"}, {"time": 1.9667, "x": 1, "y": 0.254}, {"time": 2, "x": 1, "y": 0.572, "curve": "stepped"}, {"time": 2.0333, "x": 1, "y": 0.572}, {"time": 2.0667, "x": 1, "y": 0.79}]}}}, "logo": {"slots": {"bongden0": {"attachment": [{"time": 0, "name": null}]}, "logonho1": {"attachment": [{"time": 0, "name": "logonho1"}]}, "logonho2": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "logonho2"}]}, "logonho3": {"attachment": [{"time": 0, "name": "logonho3"}]}, "logonho4": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "logonho4"}]}, "logonho5": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "logonho2"}]}, "logonho6": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffff98"}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "logonho1"}]}}, "bones": {"bone61": {"translate": [{"time": 0, "x": -28.51, "y": -7.39}, {"time": 1.3333, "x": 67.64, "y": 18.43}], "scale": [{"time": 0, "x": 0.765, "y": 1}, {"time": 0.6667, "x": 1.059, "y": 1}, {"time": 1.3333, "x": 0.765, "y": 1}]}, "root": {"scale": [{"time": 0, "x": 1.07, "y": 1.07}]}}, "deform": {"default": {"logonho4": {"logonho4": [{"time": 0, "vertices": [-23.21171, -7.35732, -21.5465, -3.34069, -12.1241, -1.65823, -6.55593, -1.02498, -3.23218, -0.11762, 0, 0, 3.58962, 0.31509, 4.02309, 0.20985, 4.41172, 0.57183, 6.6549, -0.44267, 10.38868, -0.53711, 13.33565, -2.24501, 18.77521, -4.19725, 20.25353, -7.17251]}, {"time": 0.1, "vertices": [-12.15077, -4.17524, -8.57484, -1.13982, -4.82639, -0.4983, -1.64111, -0.49854, -0.2591, 0.03428, 1.72459, 0.14164, 3.58962, 0.31509, 1.97329, -0.04747, 1.51598, 0.25311, 2.71139, -0.1317, 4.85689, 0.13022, 6.91453, -0.8953, 9.77402, -1.97703, 12.93674, -3.53789]}, {"time": 0.1667, "vertices": [-4.77681, -2.05385, -4.13364, -1.08253, -2.09648, -0.26616, 0.01596, -0.35429, 1.72295, 0.13556, 2.87432, 0.23607, 3.58962, 0.31509, 0.60676, -0.21901, -0.41451, 0.04062, 1.06686, 0.13414, 2.50294, 0.54395, 4.087, -0.21291, 6.21935, -1.12482, 9.69526, -1.8461]}, {"time": 0.3333, "vertices": [1.06373, -0.82744, 1.09991, 0.29585, 1.34198, 0.03179, 2.01224, -0.22519, 2.09304, -0.35888, 2.70953, -0.09993, 3.41139, 0.02183, -1.51059, -0.52021, -2.05825, -0.49767, -1.48646, -0.18359, -1.05253, 0.22984, -0.87068, -0.19774, 0.10383, -0.35901, 1.79535, -0.89213]}, {"time": 0.5, "vertices": [1.88165, -0.48659, 2.53645, 0.94313, 2.35731, 0.32038, 1.59883, -0.15783, 1.29596, -0.35454, 1.25323, -0.556, 1.30274, -0.35792, 0.20863, -0.5432, -1.62706, -0.53423, -2.02485, -0.10255, -2.53576, 0.64647, -2.87118, 0.35311, -3.521, 0.47964, -1.97094, 0.40547]}, {"time": 0.6667, "vertices": [2.69956, -0.14575, 1.1628, 0.80345, 0.72773, 0.35483, -0.65136, -0.46031, -1.65225, -0.51649, -2.15632, -0.86421, -3.08729, -0.02465, 3.10264, -0.3836, 0.88969, 0.14203, -0.14941, -0.17397, -1.29749, 0.54234, -2.64256, 0.19103, -3.36117, 0.19346, -3.36317, 0.41135]}, {"time": 0.8333, "vertices": [7.81317, 2.54863, 5.45786, 2.30305, 3.30249, 0.90711, 1.58729, -0.16961, -0.1556, -0.38531, -1.05894, -1.1254, -2.79262, -0.33395, 3.77182, -0.49191, -0.0195, -0.52531, -1.52071, -0.16608, -3.70278, 0.56464, -5.42251, 0.20019, -7.29224, 0.54177, -7.15483, 0.81264]}, {"time": 1, "vertices": [9.77301, 2.38054, 5.86058, 2.25441, 3.40536, 0.87627, 1.56259, 0.15946, -1.06357, -0.34119, -4.27665, -0.77544, -6.28805, -0.28331, 4.441, -0.60021, 1.49078, -0.30931, -0.98519, -0.15221, -4.06663, 0.55361, -6.3195, 0.15433, -8.87918, 0.49038, -10.9465, 1.21394]}, {"time": 1.1667, "vertices": [10.25894, 1.95798, 6.51732, 1.40807, 3.07901, 0.67427, -0.38237, 0.04219, -5.07256, -0.58635, -8.73876, -0.63337, -13.04151, -0.04552, 11.89018, 0.0589, 6.22816, 0.41907, 2.03693, 0.00473, -1.91068, 0.679, -5.27093, 0.22352, -8.70067, 0.7515, -3.36317, 0.41135]}, {"time": 1.3333, "vertices": [10.25894, 1.95798, 2.83966, 1.20138, -2.16288, 0.16014, -7.84909, -0.20671, -14.23068, -0.14979, -20.5831, 1.55478, -23.03463, 4.44189, 27.96692, 5.91386, 19.76499, 3.17993, 11.87586, 0.77718, 5.83061, 0.72378, 0.02271, 0.86252, -5.40902, 1.32674, -9.02257, 1.92264], "curve": "stepped"}, {"time": 2.6667, "vertices": [10.25894, 1.95798, 2.83966, 1.20138, -2.16288, 0.16014, -7.84909, -0.20671, -14.23068, -0.14979, -20.5831, 1.55478, -23.03463, 4.44189, 27.96692, 5.91386, 19.76499, 3.17993, 11.87586, 0.77718, 5.83061, 0.72378, 0.02271, 0.86252, -5.40902, 1.32674, -9.02257, 1.92264]}]}}}}, "minigame_icon1": {"slots": {"bongden0": {"attachment": [{"time": 0, "name": null}]}, "moi ban2": {"attachment": [{"time": 0, "name": "moi ban2"}]}, "option301": {"attachment": [{"time": 0, "name": "option301"}]}, "option302": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffc6"}, {"time": 0.3333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffffc4"}, {"time": 0.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.8333, "color": "ffffff00"}, {"time": 2, "color": "ffffffc6"}, {"time": 2.1667, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffffc4"}, {"time": 2.5, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option303": {"attachment": [{"time": 0, "name": "option303"}]}, "option304": {"attachment": [{"time": 0.4333, "name": "option304"}, {"time": 0.5333, "name": "option305"}, {"time": 0.6333, "name": "option306"}, {"time": 0.7333, "name": "option307"}, {"time": 0.8333, "name": "option308"}, {"time": 0.9333, "name": "option309"}, {"time": 1.0333, "name": "option310"}, {"time": 1.1333, "name": "option311"}, {"time": 1.2333, "name": "option312"}, {"time": 1.3333, "name": null}]}, "option305": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1, "color": "ffffff00"}, {"time": 0.2667, "color": "ffffffc6"}, {"time": 0.4333, "color": "ffffffff"}, {"time": 0.6, "color": "ffffffc4"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.9333, "color": "ffffff00"}, {"time": 2.1, "color": "ffffffc6"}, {"time": 2.2667, "color": "ffffffff"}, {"time": 2.4333, "color": "ffffffc4"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option306": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffffc6"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 0.7, "color": "ffffffc4"}, {"time": 0.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.0333, "color": "ffffff00"}, {"time": 2.2, "color": "ffffffc6"}, {"time": 2.3667, "color": "ffffffff"}, {"time": 2.5333, "color": "ffffffc4"}, {"time": 2.7, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option307": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.4667, "color": "ffffffc6"}, {"time": 0.6333, "color": "ffffffff"}, {"time": 0.8, "color": "ffffffc4"}, {"time": 0.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1333, "color": "ffffff00"}, {"time": 2.3, "color": "ffffffc6"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6333, "color": "ffffffc4"}, {"time": 2.8, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option308": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.5667, "color": "ffffffc6"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 0.9, "color": "ffffffc4"}, {"time": 1.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2333, "color": "ffffff00"}, {"time": 2.4, "color": "ffffffc6"}, {"time": 2.5667, "color": "ffffffff"}, {"time": 2.7333, "color": "ffffffc4"}, {"time": 2.9, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option309": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffc6"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1, "color": "ffffffc4"}, {"time": 1.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3333, "color": "ffffff00"}, {"time": 2.5, "color": "ffffffc6"}, {"time": 2.6667, "color": "ffffffff"}, {"time": 2.8333, "color": "ffffffc4"}, {"time": 3, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option310": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}, {"time": 0.7667, "color": "ffffffc6"}, {"time": 0.9333, "color": "ffffffff"}, {"time": 1.1, "color": "ffffffc4"}, {"time": 1.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.4333, "color": "ffffff00"}, {"time": 2.6, "color": "ffffffc6"}, {"time": 2.7667, "color": "ffffffff"}, {"time": 2.9333, "color": "ffffffc4"}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option311": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7, "color": "ffffff00"}, {"time": 0.8667, "color": "ffffffc6"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.2, "color": "ffffffc4"}, {"time": 1.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.5333, "color": "ffffff00"}, {"time": 2.7, "color": "ffffffc6"}, {"time": 2.8667, "color": "ffffffff"}, {"time": 3.0333, "color": "ffffffc4"}, {"time": 3.2, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option312": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffc6"}, {"time": 1.1333, "color": "ffffffff"}, {"time": 1.3, "color": "ffffffc4"}, {"time": 1.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6333, "color": "ffffff00"}, {"time": 2.8, "color": "ffffffc6"}, {"time": 2.9667, "color": "ffffffff"}, {"time": 3.1333, "color": "ffffffc4"}, {"time": 3.3, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option313": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 2.5, "color": "ffffffff"}, {"time": 3, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option313"}]}, "option314": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option314"}]}, "option315": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9, "color": "ffffff00"}, {"time": 1.0667, "color": "ffffffc6"}, {"time": 1.2333, "color": "ffffffff"}, {"time": 1.4, "color": "ffffffc4"}, {"time": 1.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.7333, "color": "ffffff00"}, {"time": 2.9, "color": "ffffffc6"}, {"time": 3.0667, "color": "ffffffff"}, {"time": 3.2333, "color": "ffffffc4"}, {"time": 3.4, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option316": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffff00"}, {"time": 1.1667, "color": "ffffffc6"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.5, "color": "ffffffc4"}, {"time": 1.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.8333, "color": "ffffff00"}, {"time": 3, "color": "ffffffc6"}, {"time": 3.1667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffffc4"}, {"time": 3.5, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option317": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1, "color": "ffffff00"}, {"time": 1.2667, "color": "ffffffc6"}, {"time": 1.4333, "color": "ffffffff"}, {"time": 1.6, "color": "ffffffc4"}, {"time": 1.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9333, "color": "ffffff00"}, {"time": 3.1, "color": "ffffffc6"}, {"time": 3.2667, "color": "ffffffff"}, {"time": 3.4333, "color": "ffffffc4"}, {"time": 3.6, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option318": {"color": [{"time": 0, "color": "ffffff50"}, {"time": 0.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2, "color": "ffffff00"}, {"time": 1.3667, "color": "ffffffc6"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 1.7, "color": "ffffffc4"}, {"time": 1.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0333, "color": "ffffff00"}, {"time": 3.2, "color": "ffffffc6"}, {"time": 3.3667, "color": "ffffffff"}, {"time": 3.5333, "color": "ffffffc4"}, {"time": 3.6333, "color": "ffffff50"}], "attachment": [{"time": 0, "name": "option302"}]}, "option319": {"color": [{"time": 0, "color": "ffffffc4"}, {"time": 0.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3, "color": "ffffff00"}, {"time": 1.4667, "color": "ffffffc6"}, {"time": 1.6333, "color": "ffffffff"}, {"time": 1.8, "color": "ffffffc4"}, {"time": 1.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1333, "color": "ffffff00"}, {"time": 3.3, "color": "ffffffc6"}, {"time": 3.4667, "color": "ffffffff"}, {"time": 3.6333, "color": "ffffffc4"}], "attachment": [{"time": 0, "name": "option302"}]}, "option320": {"color": [{"time": 0, "color": "ffffffdb"}, {"time": 0.1, "color": "ffffffc4"}, {"time": 0.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4, "color": "ffffff00"}, {"time": 1.5667, "color": "ffffffc6"}, {"time": 1.7333, "color": "ffffffff"}, {"time": 1.9, "color": "ffffffc4"}, {"time": 2.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.2333, "color": "ffffff00"}, {"time": 3.4, "color": "ffffffc6"}, {"time": 3.5667, "color": "ffffffff"}, {"time": 3.6333, "color": "ffffffdb"}], "attachment": [{"time": 0, "name": "option302"}]}, "option321": {"color": [{"time": 0, "color": "fffffff1"}, {"time": 0.0333, "color": "ffffffff"}, {"time": 0.2, "color": "ffffffc4"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffc6"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 2, "color": "ffffffc4"}, {"time": 2.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.3333, "color": "ffffff00"}, {"time": 3.5, "color": "ffffffc6"}, {"time": 3.6333, "color": "fffffff1"}], "attachment": [{"time": 0, "name": "option302"}]}, "option322": {"color": [{"time": 0, "color": "ffffffd4"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.3, "color": "ffffffc4"}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6, "color": "ffffff00"}, {"time": 1.7667, "color": "ffffffc6"}, {"time": 1.9333, "color": "ffffffff"}, {"time": 2.1, "color": "ffffffc4"}, {"time": 2.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.4333, "color": "ffffff00"}, {"time": 3.6, "color": "ffffffc6"}], "attachment": [{"time": 0, "name": "option302"}]}, "option323": {"color": [{"time": 0, "color": "ffffff7a"}, {"time": 0.0667, "color": "ffffffc6"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.4, "color": "ffffffc4"}, {"time": 0.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7333, "color": "ffffff00"}, {"time": 1.9, "color": "ffffffc6"}, {"time": 2.0667, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffffc4"}, {"time": 2.4, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option324": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffc6"}, {"time": 0.3333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffffc4"}, {"time": 0.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.8333, "color": "ffffff00"}, {"time": 2, "color": "ffffffc6"}, {"time": 2.1667, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffffc4"}, {"time": 2.5, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option325": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1, "color": "ffffff00"}, {"time": 0.2667, "color": "ffffffc6"}, {"time": 0.4333, "color": "ffffffff"}, {"time": 0.6, "color": "ffffffc4"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.9333, "color": "ffffff00"}, {"time": 2.1, "color": "ffffffc6"}, {"time": 2.2667, "color": "ffffffff"}, {"time": 2.4333, "color": "ffffffc4"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option326": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffffc6"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 0.7, "color": "ffffffc4"}, {"time": 0.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.0333, "color": "ffffff00"}, {"time": 2.2, "color": "ffffffc6"}, {"time": 2.3667, "color": "ffffffff"}, {"time": 2.5333, "color": "ffffffc4"}, {"time": 2.7, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option327": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.4667, "color": "ffffffc6"}, {"time": 0.6333, "color": "ffffffff"}, {"time": 0.8, "color": "ffffffc4"}, {"time": 0.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1333, "color": "ffffff00"}, {"time": 2.3, "color": "ffffffc6"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.6333, "color": "ffffffc4"}, {"time": 2.8, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option328": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4667, "color": "ffffff00"}, {"time": 0.6333, "color": "ffffffc6"}, {"time": 0.8, "color": "ffffffff"}, {"time": 0.9667, "color": "ffffffc4"}, {"time": 1.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2333, "color": "ffffff00"}, {"time": 2.4, "color": "ffffffc6"}, {"time": 2.5667, "color": "ffffffff"}, {"time": 2.7333, "color": "ffffffc4"}, {"time": 2.9, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option329": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffc6"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1, "color": "ffffffc4"}, {"time": 1.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3333, "color": "ffffff00"}, {"time": 2.5, "color": "ffffffc6"}, {"time": 2.6667, "color": "ffffffff"}, {"time": 2.8333, "color": "ffffffc4"}, {"time": 3, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option330": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}, {"time": 0.7667, "color": "ffffffc6"}, {"time": 0.9333, "color": "ffffffff"}, {"time": 1.1, "color": "ffffffc4"}, {"time": 1.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.4333, "color": "ffffff00"}, {"time": 2.6, "color": "ffffffc6"}, {"time": 2.7667, "color": "ffffffff"}, {"time": 2.9333, "color": "ffffffc4"}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option331": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7, "color": "ffffff00"}, {"time": 0.8667, "color": "ffffffc6"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.2, "color": "ffffffc4"}, {"time": 1.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.5333, "color": "ffffff00"}, {"time": 2.7, "color": "ffffffc6"}, {"time": 2.8667, "color": "ffffffff"}, {"time": 3.0333, "color": "ffffffc4"}, {"time": 3.2, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option332": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffc6"}, {"time": 1.1333, "color": "ffffffff"}, {"time": 1.3, "color": "ffffffc4"}, {"time": 1.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6333, "color": "ffffff00"}, {"time": 2.8, "color": "ffffffc6"}, {"time": 2.9667, "color": "ffffffff"}, {"time": 3.1333, "color": "ffffffc4"}, {"time": 3.3, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option333": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9, "color": "ffffff00"}, {"time": 1.0667, "color": "ffffffc6"}, {"time": 1.2333, "color": "ffffffff"}, {"time": 1.4, "color": "ffffffc4"}, {"time": 1.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.7333, "color": "ffffff00"}, {"time": 2.9, "color": "ffffffc6"}, {"time": 3.0667, "color": "ffffffff"}, {"time": 3.2333, "color": "ffffffc4"}, {"time": 3.4, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option334": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffff00"}, {"time": 1.1667, "color": "ffffffc6"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.5, "color": "ffffffc4"}, {"time": 1.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.8333, "color": "ffffff00"}, {"time": 3, "color": "ffffffc6"}, {"time": 3.1667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffffc4"}, {"time": 3.5, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option335": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1, "color": "ffffff00"}, {"time": 1.2667, "color": "ffffffc6"}, {"time": 1.4333, "color": "ffffffff"}, {"time": 1.6, "color": "ffffffc4"}, {"time": 1.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9333, "color": "ffffff00"}, {"time": 3.1, "color": "ffffffc6"}, {"time": 3.2667, "color": "ffffffff"}, {"time": 3.4333, "color": "ffffffc4"}, {"time": 3.6, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "option302"}]}, "option336": {"color": [{"time": 0, "color": "ffffff52"}, {"time": 0.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2, "color": "ffffff00"}, {"time": 1.3667, "color": "ffffffc6"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 1.7, "color": "ffffffc4"}, {"time": 1.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0333, "color": "ffffff00"}, {"time": 3.2, "color": "ffffffc6"}, {"time": 3.3667, "color": "ffffffff"}, {"time": 3.5333, "color": "ffffffc4"}, {"time": 3.6333, "color": "ffffff52"}], "attachment": [{"time": 0, "name": "option302"}]}, "option337": {"color": [{"time": 0, "color": "ffffffc4"}, {"time": 0.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3, "color": "ffffff00"}, {"time": 1.4667, "color": "ffffffc6"}, {"time": 1.6333, "color": "ffffffff"}, {"time": 1.8, "color": "ffffffc4"}, {"time": 1.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1333, "color": "ffffff00"}, {"time": 3.3, "color": "ffffffc6"}, {"time": 3.4667, "color": "ffffffff"}, {"time": 3.6333, "color": "ffffffc4"}], "attachment": [{"time": 0, "name": "option302"}]}, "option338": {"color": [{"time": 0, "color": "ffffffe8"}, {"time": 0.1, "color": "ffffffc4"}, {"time": 0.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4, "color": "ffffff00"}, {"time": 1.5667, "color": "ffffffc6"}, {"time": 1.7333, "color": "ffffffff"}, {"time": 1.9, "color": "ffffffc6"}, {"time": 2.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.2333, "color": "ffffff00"}, {"time": 3.4, "color": "ffffffc6"}, {"time": 3.5667, "color": "ffffffff"}, {"time": 3.6333, "color": "ffffffe8"}], "attachment": [{"time": 0, "name": "option302"}]}, "option339": {"color": [{"time": 0, "color": "fffffff5"}, {"time": 0.0333, "color": "ffffffff"}, {"time": 0.2, "color": "ffffffc4"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffc6"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 2, "color": "ffffffc4"}, {"time": 2.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.3333, "color": "ffffff00"}, {"time": 3.5, "color": "ffffffc6"}, {"time": 3.6333, "color": "fffffff5"}], "attachment": [{"time": 0, "name": "option302"}]}, "option340": {"color": [{"time": 0, "color": "ffffffd2"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.3, "color": "ffffffc4"}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6, "color": "ffffff00"}, {"time": 1.7667, "color": "ffffffc6"}, {"time": 1.9333, "color": "ffffffff"}, {"time": 2.1, "color": "ffffffc4"}, {"time": 2.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.4333, "color": "ffffff00"}, {"time": 3.6, "color": "ffffffc6"}, {"time": 3.6333, "color": "ffffffd2"}], "attachment": [{"time": 0, "name": "option302"}]}, "option341": {"color": [{"time": 0, "color": "ffffff76"}, {"time": 0.0667, "color": "ffffffc6"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.4, "color": "ffffffc4"}, {"time": 0.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7, "color": "ffffff00"}, {"time": 1.8667, "color": "ffffffc6"}, {"time": 2.0333, "color": "ffffffff"}, {"time": 2.2, "color": "ffffffc4"}, {"time": 2.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.5333, "color": "ffffff00"}, {"time": 3.6333, "color": "ffffff76"}], "attachment": [{"time": 0, "name": "option302"}]}}}, "minigameicon_tai": {"slots": {"bongden0": {"attachment": [{"time": 0, "name": null}]}, "tai": {"attachment": [{"time": 0, "name": "tai"}]}, "tai2": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.5, "color": "ffffff64"}, {"time": 1, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "tai"}]}}, "bones": {"bone93": {"scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.5, "x": 1.193, "y": 1.193}, {"time": 1, "x": 1, "y": 1}]}}}, "minigameicon_xiu": {"slots": {"bongden0": {"attachment": [{"time": 0, "name": null}]}, "xiu": {"attachment": [{"time": 0, "name": "xiu"}]}, "xiu2": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.5, "color": "ffffff3c"}, {"time": 1, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "xiu"}]}}, "bones": {"bone92": {"scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.5, "x": 1.185, "y": 1.185}, {"time": 1, "x": 1, "y": 1}]}}}, "moiban": {"slots": {"bongden0": {"attachment": [{"time": 0, "name": null}]}, "moi ban2": {"attachment": [{"time": 0, "name": "moi ban2"}]}, "moi ban3": {"color": [{"time": 0.3333, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "moi ban3"}]}}, "bones": {"bone143": {"translate": [{"time": 0, "x": -28.78, "y": -20.25}], "scale": [{"time": 0, "x": 0.719, "y": 0.719}, {"time": 0.3333, "x": 0.94, "y": 0.94}, {"time": 0.6667, "x": 1.029, "y": 1.029, "curve": "stepped"}, {"time": 1.3333, "x": 1.029, "y": 1.029}]}}}, "ruttien": {"slots": {"bongden0": {"attachment": [{"time": 0, "name": null}]}, "ruttien0": {"attachment": [{"time": 0, "name": "ruttien0"}]}, "ruttien1": {"attachment": [{"time": 0, "name": "ruttien1"}]}, "ruttien2": {"attachment": [{"time": 0, "name": "ruttien2"}]}, "ruttien3": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7, "color": "ffffff00"}, {"time": 2.4667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "ruttien2"}]}, "ruttien4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9, "color": "ffffff00"}, {"time": 1.3, "color": "ffffff38"}, {"time": 1.6667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "ruttien2"}]}, "star3": {"attachment": [{"time": 0, "name": "star"}]}}, "bones": {"bone38": {"rotate": [{"time": 0, "angle": 21.35}, {"time": 0.6667, "angle": -18.66}, {"time": 0.8667, "angle": -23.96, "curve": "stepped"}, {"time": 1.7, "angle": -23.96}, {"time": 1.8333, "angle": -18.66}, {"time": 2.4667, "angle": 21.35}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 18.36, "y": 2.44}, {"time": 0.8667, "x": 22.81, "y": 1.04, "curve": "stepped"}, {"time": 1.7, "x": 22.81, "y": 1.04}, {"time": 1.8333, "x": 18.36, "y": 2.44}, {"time": 2.4667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1.15, "y": 1.15}, {"time": 0.8667, "x": 1.219, "y": 1.219, "curve": "stepped"}, {"time": 1.7, "x": 1.219, "y": 1.219}, {"time": 1.8333, "x": 1.15, "y": 1.15}, {"time": 2.4667, "x": 1, "y": 1}]}, "bone39": {"rotate": [{"time": 0.7667, "angle": 0}, {"time": 1.3333, "angle": -176.4}, {"time": 1.6667, "angle": 92.48}, {"time": 1.9667, "angle": 0}], "translate": [{"time": 0.7667, "x": -3.31, "y": 0.18}, {"time": 0.9, "x": -0.37, "y": -0.37, "curve": "stepped"}, {"time": 1.6667, "x": -0.37, "y": -0.37}, {"time": 1.9667, "x": -11.59, "y": 1.47}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7667, "x": 0, "y": 0}, {"time": 1.3333, "x": 2, "y": 2}, {"time": 1.9667, "x": 0, "y": 0}]}, "bone40": {"rotate": [{"time": 0, "angle": 21.35}, {"time": 0.6667, "angle": -18.66}, {"time": 0.8667, "angle": -23.96, "curve": "stepped"}, {"time": 1.7, "angle": -23.96}, {"time": 1.8333, "angle": -18.66}, {"time": 2.4667, "angle": 21.35}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 18.36, "y": 2.44}, {"time": 0.8667, "x": 22.81, "y": 1.04, "curve": "stepped"}, {"time": 1.7, "x": 22.81, "y": 1.04}, {"time": 1.8333, "x": 18.36, "y": 2.44}, {"time": 2.4667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1.15, "y": 1.15}, {"time": 0.8667, "x": 1.219, "y": 1.219, "curve": "stepped"}, {"time": 1.7, "x": 1.219, "y": 1.219}, {"time": 1.8333, "x": 1.15, "y": 1.15}, {"time": 2.4667, "x": 1, "y": 1}]}, "bone41": {"rotate": [{"time": 0, "angle": 21.35}, {"time": 0.6667, "angle": -18.66}, {"time": 0.8667, "angle": -23.96, "curve": "stepped"}, {"time": 1.7, "angle": -23.96}, {"time": 1.8333, "angle": -18.66}, {"time": 2.4667, "angle": 21.35}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 18.36, "y": 2.44}, {"time": 0.8667, "x": 22.81, "y": 1.04, "curve": "stepped"}, {"time": 1.7, "x": 22.81, "y": 1.04}, {"time": 1.8333, "x": 18.36, "y": 2.44}, {"time": 2.4667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1.15, "y": 1.15}, {"time": 0.8667, "x": 1.219, "y": 1.219, "curve": "stepped"}, {"time": 1.7, "x": 1.219, "y": 1.219}, {"time": 1.8333, "x": 1.15, "y": 1.15}, {"time": 2.4667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1}]}}, "deform": {"default": {"ruttien0": {"ruttien0": [{"time": 0}, {"time": 0.5333, "vertices": [4.94397, -1.85399, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.94397, -1.85399, 0, 0, 9.06396, -4.53197, 10.50596, 0.41201, 0, 0, 7.82797, -2.88398, 8.23997, 0.20601], "curve": "stepped"}, {"time": 1.6667, "vertices": [4.94397, -1.85399, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.94397, -1.85399, 0, 0, 9.06396, -4.53197, 10.50596, 0.41201, 0, 0, 7.82797, -2.88398, 8.23997, 0.20601]}, {"time": 2.1333}]}}}}, "sukien": {"slots": {"bongden0": {"attachment": [{"time": 0, "name": null}]}, "hopqua1": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.2667, "color": "ffffff8d"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1.0667, "color": "ffffff8d"}, {"time": 1.4667, "color": "ffffffff"}, {"time": 1.8667, "color": "ffffff8d"}, {"time": 2.2667, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff8d"}, {"time": 3.0667, "color": "ffffffff"}, {"time": 3.4667, "color": "ffffff8d"}, {"time": 3.8667, "color": "ffffffff"}, {"time": 4.2667, "color": "ffffff8d"}, {"time": 4.6667, "color": "ffffffff"}, {"time": 5.0667, "color": "ffffff8d"}, {"time": 5.4667, "color": "ffffffff"}, {"time": 5.8667, "color": "ffffff8d"}, {"time": 6.2667, "color": "ffffffff"}, {"time": 6.6667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "hopqua1"}]}, "hopqua2": {"attachment": [{"time": 0, "name": "hopqua2"}]}, "hopqua3": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "hopqua3"}]}}, "bones": {"bone37": {"rotate": [{"time": 0, "angle": 43.36, "curve": "stepped"}, {"time": 0.2667, "angle": 43.36}, {"time": 1.0667, "angle": 7.19}, {"time": 1.3333, "angle": 0.82}], "translate": [{"time": 0, "x": -10.7, "y": 0.31}, {"time": 0.2667, "x": -0.39, "y": 0.08}, {"time": 1.3333, "x": 52.92, "y": -11.36}], "scale": [{"time": 0, "x": 0, "y": 0}, {"time": 0.2667, "x": 0.686, "y": 0.686}, {"time": 0.6667, "x": 0.813, "y": 0.813}, {"time": 1.0667, "x": 0.686, "y": 0.686}, {"time": 1.3333, "x": 0.366, "y": 0.366}]}}}, "sukien2": {"slots": {"bongden0": {"attachment": [{"time": 0, "name": null}]}, "hopqua1": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffff8d"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 1, "color": "ffffff8d"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffff8d"}, {"time": 2, "color": "ffffff00"}, {"time": 2.3333, "color": "ffffff8d"}, {"time": 2.6667, "color": "ffffff00"}, {"time": 3, "color": "ffffff8d"}, {"time": 3.3333, "color": "ffffff00"}, {"time": 3.6667, "color": "ffffff8d"}, {"time": 4, "color": "ffffff00"}, {"time": 4.3333, "color": "ffffff8d"}, {"time": 4.6667, "color": "ffffff00"}, {"time": 5, "color": "ffffff8d"}, {"time": 5.3333, "color": "ffffff00"}, {"time": 5.6667, "color": "ffffff8d"}, {"time": 6, "color": "ffffff00"}, {"time": 6.3333, "color": "ffffff8d"}, {"time": 6.6, "color": "ffffff00"}, {"time": 6.8667, "color": "ffffff8d"}, {"time": 7.1333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "hopqua1"}]}, "hopqua2": {"attachment": [{"time": 0, "name": "hopqua2"}]}, "hopqua3": {"color": [{"time": 0, "color": "ffffff00"}]}, "lvlup0": {"color": [{"time": 0, "color": "c6d4ffff"}], "attachment": [{"time": 0, "name": "lvlup0"}, {"time": 0.1, "name": "lvlup1"}, {"time": 0.2, "name": "lvlup2"}, {"time": 0.3, "name": "lvlup3"}, {"time": 0.4, "name": "lvlup4"}, {"time": 0.5, "name": "lvlup5"}, {"time": 0.6, "name": "lvlup6"}, {"time": 0.6667, "name": "lvlup7"}, {"time": 0.7667, "name": "lvlup8"}, {"time": 0.8667, "name": "lvlup0"}, {"time": 0.9667, "name": "lvlup1"}, {"time": 1.0667, "name": "lvlup2"}, {"time": 1.1667, "name": "lvlup3"}, {"time": 1.2667, "name": "lvlup4"}, {"time": 1.3667, "name": "lvlup5"}, {"time": 1.4667, "name": "lvlup6"}, {"time": 1.5667, "name": "lvlup7"}, {"time": 1.6667, "name": "lvlup8"}, {"time": 1.7667, "name": "lvlup0"}, {"time": 1.8333, "name": "lvlup1"}, {"time": 1.9333, "name": "lvlup2"}, {"time": 2.0333, "name": "lvlup3"}, {"time": 2.1333, "name": "lvlup4"}, {"time": 2.2333, "name": "lvlup5"}, {"time": 2.3333, "name": "lvlup6"}, {"time": 2.4333, "name": "lvlup7"}, {"time": 2.5333, "name": "lvlup8"}, {"time": 2.6333, "name": "lvlup0"}, {"time": 2.7333, "name": "lvlup1"}, {"time": 2.8333, "name": "lvlup2"}, {"time": 2.9333, "name": "lvlup3"}, {"time": 3, "name": "lvlup4"}, {"time": 3.1, "name": "lvlup5"}, {"time": 3.2, "name": "lvlup6"}, {"time": 3.3, "name": "lvlup7"}, {"time": 3.4, "name": "lvlup8"}, {"time": 3.5, "name": "lvlup0"}, {"time": 3.7, "name": "lvlup1"}, {"time": 3.8, "name": "lvlup2"}, {"time": 3.9, "name": "lvlup3"}, {"time": 4, "name": "lvlup4"}, {"time": 4.1, "name": "lvlup5"}, {"time": 4.2, "name": "lvlup6"}, {"time": 4.2667, "name": "lvlup7"}, {"time": 4.3667, "name": "lvlup8"}, {"time": 4.4667, "name": "lvlup0"}, {"time": 4.5667, "name": "lvlup1"}, {"time": 4.6667, "name": "lvlup2"}, {"time": 4.7667, "name": "lvlup3"}, {"time": 4.8667, "name": "lvlup4"}, {"time": 4.9667, "name": "lvlup5"}, {"time": 5.0667, "name": "lvlup6"}, {"time": 5.1667, "name": "lvlup7"}, {"time": 5.2667, "name": "lvlup8"}, {"time": 5.3667, "name": "lvlup0"}, {"time": 5.4333, "name": "lvlup1"}, {"time": 5.5333, "name": "lvlup2"}, {"time": 5.6333, "name": "lvlup3"}, {"time": 5.7333, "name": "lvlup4"}, {"time": 5.8333, "name": "lvlup5"}, {"time": 5.9333, "name": "lvlup6"}, {"time": 6.0333, "name": "lvlup7"}, {"time": 6.1333, "name": "lvlup8"}, {"time": 6.2333, "name": "lvlup0"}, {"time": 6.3333, "name": "lvlup1"}, {"time": 6.4333, "name": "lvlup2"}, {"time": 6.5333, "name": "lvlup3"}, {"time": 6.6, "name": "lvlup4"}, {"time": 6.7, "name": "lvlup5"}, {"time": 6.8, "name": "lvlup6"}, {"time": 6.9, "name": "lvlup7"}, {"time": 7, "name": "lvlup8"}, {"time": 7.1, "name": "lvlup0"}]}}, "bones": {"bone37": {"rotate": [{"time": 0, "angle": 43.36, "curve": "stepped"}, {"time": 0.2667, "angle": 43.36}, {"time": 1.0667, "angle": 7.19}, {"time": 1.3333, "angle": 0.82}], "translate": [{"time": 0, "x": -10.7, "y": 0.31}, {"time": 0.2667, "x": -0.39, "y": 0.08}, {"time": 1.3333, "x": 52.92, "y": -11.36}], "scale": [{"time": 0, "x": 0, "y": 0}, {"time": 0.2667, "x": 0.686, "y": 0.686}, {"time": 0.6667, "x": 0.813, "y": 0.813}, {"time": 1.0667, "x": 0.686, "y": 0.686}, {"time": 1.3333, "x": 0.366, "y": 0.366}]}, "bone144": {"translate": [{"time": 0, "x": 0, "y": -5.21}], "scale": [{"time": 0, "x": 0.922, "y": 1.133}]}}}, "thanhmoiban": {"slots": {"bongden0": {"attachment": [{"time": 0, "name": null}]}, "light1": {"color": [{"time": 0, "color": "23bdffff"}], "attachment": [{"time": 0, "name": "light0"}]}, "light2": {"color": [{"time": 0, "color": "22bcffff"}], "attachment": [{"time": 0, "name": "light0"}]}, "thanhmoiban": {"attachment": [{"time": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}]}}, "bones": {"bone118": {"scale": [{"time": 0, "x": 1, "y": 0.251}]}, "bone94": {"scale": [{"time": 0, "x": 1, "y": 0.249}]}}, "paths": {"moiban": {"position": [{"time": 0, "position": 0.5, "curve": [0.697, 0.4, 0.535, 0.73]}, {"time": 1.6667, "position": 1.5}]}, "moiban2": {"position": [{"time": 0, "curve": [0.697, 0.4, 0.535, 0.73]}, {"time": 1.6667, "position": 1}]}}}, "thongbaokichhoat": {"slots": {"bongden0": {"attachment": [{"time": 0, "name": null}]}, "light0": {"attachment": [{"time": 0, "name": "light0"}]}}, "paths": {"path": {"position": [{"time": 0}, {"time": 1.6667, "position": -1}]}}}}}